import concurrent.futures
import time

from cime.cime_topo_parser import <PERSON><PERSON><PERSON>ars<PERSON>
from qs.qs_status_parser import <PERSON><PERSON>tatusParser
from qs.topo_cleaner import TopologyCleaner
from qs.topo_matcher import TopologyMatcher
from config.logger_config import get_logger

logger = get_logger(__name__)

# -----------------------------
# 文件路径
# -----------------------------
CIME_FILE = "./data/万州_20250829_1614.CIME"
QS_FILE = "./data/重庆_20250721_0000.QS"
CIME_TOPOLOGY_FILE = "./out/cime_topology.json"
SINGLE_NODE_EDGES_FILE = "./out/single_node_edges.json"
QS_STATUS_FILE = "./out/qs_status.json"
MATCHED_OUTPUT = "./out/combined_topology_with_match.json"
UNMATCHED_OUTPUT = "./out/unmatched_elements.json"
CLEAN_OUTPUT = "./out/clean_topology.json"
ISLAND_OUTPUT = "./out/cime_islands.json"

if __name__ == '__main__':
    start_time = time.time()

    # -----------------------------
    # 并行解析 CIME 和 QS
    # -----------------------------
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future_cime = executor.submit(lambda: CIMEParser(CIME_FILE).run(CIME_TOPOLOGY_FILE, SINGLE_NODE_EDGES_FILE))
        future_qs = executor.submit(lambda: QSStatusParser(QS_FILE).run(QS_STATUS_FILE))
        topo = future_cime.result()
        qs_status = future_qs.result()

    logger.info("CIME 拓扑和 QS 状态解析完成")

    # -----------------------------
    # 拓扑匹配
    # -----------------------------
    matcher = TopologyMatcher(CIME_TOPOLOGY_FILE, QS_STATUS_FILE, threshold=80)
    matcher.run(matched_topo_path=MATCHED_OUTPUT, unmatched_path=UNMATCHED_OUTPUT)

    logger.info(f"带匹配信息的拓扑已生成: {MATCHED_OUTPUT}")
    logger.info(f"未匹配的节点和设备已生成: {UNMATCHED_OUTPUT}")

    # -----------------------------
    # 清洗拓扑
    # -----------------------------
    cleaner = TopologyCleaner(MATCHED_OUTPUT)
    cleaner.run(CLEAN_OUTPUT)

    logger.info(f"状态干净的拓扑已生成: {CLEAN_OUTPUT}")

    # -----------------------------
    # 岛划分
    # -----------------------------
    run_island_partition(CLEAN_OUTPUT, ISLAND_OUTPUT, consider_status=False)
    logger.info(f"拓扑岛划分结果已生成: {ISLAND_OUTPUT}")

    logger.info(f"总耗时: {time.time() - start_time:.2f}s")
