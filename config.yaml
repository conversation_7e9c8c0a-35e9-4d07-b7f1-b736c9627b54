# 拓扑分析器配置文件

# 环境设置
environment: "development"
debug: true

# 文件路径配置
file_paths:
  data_dir: "./data"
  output_dir: "./out"
  
  # 默认输入文件（可通过命令行参数覆盖）
  cime_file: "./data/万州_20250829_1614.CIME"
  qs_file: "./data/重庆_20250721_0000.QS"
  
  # 输出文件名
  cime_topology_file: "cime_topology.json"
  single_node_edges_file: "single_node_edges.json"
  qs_status_file: "qs_status.json"
  matched_output: "combined_topology_with_match.json"
  unmatched_output: "unmatched_elements.json"
  clean_output: "clean_topology.json"
  island_output: "cime_islands.json"

# 处理配置
processing:
  match_threshold: 80
  cime_encoding: "gbk"
  qs_encoding: "gbk"
  max_workers: 4
  consider_status: false

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s [%(levelname)s] [%(name)s] %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S"
  file_path: "./logs/topo_analyzer.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5

# 数据库配置（如果需要）
database:
  host: "localhost"
  port: 5432
  username: "postgres"
  password: ""
  database: "topo_analyzer"
