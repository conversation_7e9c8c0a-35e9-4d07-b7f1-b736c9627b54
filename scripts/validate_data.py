#!/usr/bin/env python3
"""
数据验证脚本

验证CIME和QS文件的格式和内容
"""

import argparse
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from topo_analyzer.cime.parser import CIMEParser, CIMEParseError
from topo_analyzer.qs.status_parser import QSStatusParser, QSParseError
from topo_analyzer.config.logger_config import get_logger

logger = get_logger(__name__)


def validate_cime_file(file_path: str) -> bool:
    """验证CIME文件"""
    try:
        logger.info(f"验证CIME文件: {file_path}")
        
        parser = CIMEParser(file_path)
        parser.parse_file()
        
        # 检查基本结构
        required_blocks = ["BaseVoltage", "Substation"]
        missing_blocks = [block for block in required_blocks if block not in parser.blocks]
        
        if missing_blocks:
            logger.warning(f"缺少必需的块: {missing_blocks}")
        
        # 统计信息
        total_blocks = len(parser.blocks)
        logger.info(f"解析到 {total_blocks} 个数据块")
        
        for block_name, block_data in parser.blocks.items():
            data_count = len(block_data.get("data", []))
            logger.info(f"  {block_name}: {data_count} 条记录")
        
        # 尝试完整解析
        parser.parse_base_voltages()
        parser.parse_substations()
        parser.parse_nodes()
        parser.parse_devices()
        
        logger.info(f"节点数: {len(parser.nodes)}")
        logger.info(f"设备连接数: {sum(len(connections) for connections in parser.i_j_map.values())}")
        
        logger.info("CIME文件验证通过")
        return True
        
    except CIMEParseError as e:
        logger.error(f"CIME文件解析错误: {e}")
        return False
    except Exception as e:
        logger.error(f"CIME文件验证失败: {e}")
        return False


def validate_qs_file(file_path: str) -> bool:
    """验证QS文件"""
    try:
        logger.info(f"验证QS文件: {file_path}")
        
        parser = QSStatusParser(file_path)
        parser.parse_file()
        parser.parse_blocks()
        
        # 检查基本结构
        common_blocks = ["Bus", "Breaker", "Disconnector", "ACline"]
        found_blocks = [block for block in common_blocks if block in parser.blocks]
        
        if not found_blocks:
            logger.warning("未找到常见的设备块")
        
        # 统计信息
        total_blocks = len(parser.blocks)
        logger.info(f"解析到 {total_blocks} 个数据块")
        
        for block_name, block_data in parser.blocks.items():
            data_count = len(block_data.get("data", []))
            logger.info(f"  {block_name}: {data_count} 条记录")
        
        # 尝试提取状态
        parser.extract_node_status()
        parser.extract_device_status()
        
        logger.info(f"节点状态数: {len(parser.node_status)}")
        logger.info(f"设备状态数: {len(parser.device_status)}")
        
        # 状态分布统计
        stats = parser.get_statistics()
        logger.info(f"节点状态分布: {stats['node_status_distribution']}")
        logger.info(f"设备状态分布: {stats['device_status_distribution']}")
        
        logger.info("QS文件验证通过")
        return True
        
    except QSParseError as e:
        logger.error(f"QS文件解析错误: {e}")
        return False
    except Exception as e:
        logger.error(f"QS文件验证失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="验证CIME和QS文件")
    parser.add_argument("--cime-file", type=str, help="CIME文件路径")
    parser.add_argument("--qs-file", type=str, help="QS文件路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if not args.cime_file and not args.qs_file:
        parser.error("请至少指定一个文件进行验证")
    
    success = True
    
    try:
        if args.cime_file:
            if not Path(args.cime_file).exists():
                logger.error(f"CIME文件不存在: {args.cime_file}")
                success = False
            else:
                success &= validate_cime_file(args.cime_file)
        
        if args.qs_file:
            if not Path(args.qs_file).exists():
                logger.error(f"QS文件不存在: {args.qs_file}")
                success = False
            else:
                success &= validate_qs_file(args.qs_file)
        
        if success:
            logger.info("所有文件验证通过")
        else:
            logger.error("文件验证失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
