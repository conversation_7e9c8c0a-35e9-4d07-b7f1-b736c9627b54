#!/usr/bin/env python3
"""
运行拓扑分析的示例脚本

这个脚本展示了如何使用拓扑分析器进行完整的分析流程
"""

import argparse
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from topo_analyzer import TopologyAnalyzer, Settings
from topo_analyzer.config.logger_config import get_logger

logger = get_logger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行拓扑分析")
    parser.add_argument("--config", "-c", type=str, help="配置文件路径")
    parser.add_argument("--cime-file", type=str, help="CIME文件路径")
    parser.add_argument("--qs-file", type=str, help="QS文件路径")
    parser.add_argument("--output-dir", "-o", type=str, help="输出目录")
    parser.add_argument("--threshold", type=int, help="匹配阈值")
    parser.add_argument("--consider-status", action="store_true", help="岛屿分析时考虑设备状态")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        if args.config:
            settings = Settings.from_yaml(args.config)
        else:
            settings = Settings()
        
        # 覆盖命令行参数
        if args.cime_file:
            settings.file_paths.cime_file = args.cime_file
        if args.qs_file:
            settings.file_paths.qs_file = args.qs_file
        if args.output_dir:
            settings.file_paths.output_dir = Path(args.output_dir)
        if args.threshold:
            settings.processing.match_threshold = args.threshold
        if args.verbose:
            settings.logging.level = "DEBUG"
        
        # 确保目录存在
        settings.ensure_directories()
        
        # 创建分析器
        analyzer = TopologyAnalyzer(settings)
        
        logger.info("开始拓扑分析...")
        
        # 运行分析
        result = analyzer.run_complete_analysis(
            consider_status=args.consider_status
        )
        
        if result['success']:
            logger.info("分析完成！")
            logger.info(f"总耗时: {result['elapsed_time']:.2f} 秒")
            
            # 打印结果摘要
            print("\n=== 分析结果摘要 ===")
            print(f"CIME解析: {result['cime_analysis']['nodes']} 节点, {result['cime_analysis']['edges']} 边")
            print(f"QS解析: {result['qs_analysis']['node_status']} 节点状态, {result['qs_analysis']['device_status']} 设备状态")
            print(f"拓扑匹配: 节点匹配率 {result['topology_matching']['node_match_rate']:.2%}, 设备匹配率 {result['topology_matching']['device_match_rate']:.2%}")
            print(f"拓扑清洗: 保留 {result['topology_cleaning']['retained_edges']} 条边")
            print(f"岛屿分析: {'成功' if result['island_analysis']['success'] else '失败'}")
            
            print(f"\n输出文件:")
            for name, path in result['output_files'].items():
                print(f"  {name}: {path}")
            
        else:
            logger.error(f"分析失败: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
