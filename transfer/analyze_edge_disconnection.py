#!/usr/bin/env python3
"""
分析为什么修改一个设备状态会导致多条边断开
"""

import json
import sys


def analyze_edge_disconnection():
    """分析边断开的原因"""
    
    print("🔍 分析边断开原因")
    print("="*60)
    
    # 读取原始和检修拓扑
    with open("../out/clean_topology.json", "r", encoding="utf-8") as f:
        clean_data = json.load(f)
    
    with open("../out/maintenance_topology.json", "r", encoding="utf-8") as f:
        maint_data = json.load(f)
    
    # 我们修改的设备
    modified_device = "重庆.万州.城北站/10kV.901手车刀闸"
    
    print(f"🔧 我们修改的设备: {modified_device}")
    print()
    
    # 查找包含这个设备的边
    print("📋 查找包含此设备的边:")
    edges_with_device = []
    
    for i, edge in enumerate(clean_data["edges"]):
        devices_status = edge.get("devices_status", [])
        for dev in devices_status:
            if dev.get("name") == modified_device or dev.get("matched_name") == modified_device:
                edges_with_device.append((i, edge))
                break
    
    print(f"找到 {len(edges_with_device)} 条包含此设备的边")
    print()
    
    # 分析每条边的状态变化
    for edge_idx, edge in edges_with_device:
        from_node = edge["from"]
        to_node = edge["to"]
        
        print(f"边 {edge_idx + 1}: {from_node} <-> {to_node}")
        
        # 原始状态
        print("  原始设备状态:")
        for dev in edge.get("devices_status", []):
            name = dev.get("matched_name") or dev.get("name")
            status = dev.get("status")
            marker = " 🔧" if name == modified_device else ""
            print(f"    - {name}: {status}{marker}")
        
        # 检修后状态
        maint_edge = maint_data["edges"][edge_idx]
        print("  检修后设备状态:")
        for dev in maint_edge.get("devices_status", []):
            name = dev.get("matched_name") or dev.get("name")
            status = dev.get("status")
            marker = " 🔧" if name == modified_device else ""
            print(f"    - {name}: {status}{marker}")
        
        # 判断边是否断开
        open_devices_orig = [dev["name"] for dev in edge.get("devices_status", []) if dev.get("status") == "OPEN"]
        open_devices_maint = [dev["name"] for dev in maint_edge.get("devices_status", []) if dev.get("status") == "OPEN"]
        
        edge_disconnected_orig = len(open_devices_orig) > 0
        edge_disconnected_maint = len(open_devices_maint) > 0
        
        print(f"  边状态: 原始={'断开' if edge_disconnected_orig else '连通'} -> 检修后={'断开' if edge_disconnected_maint else '连通'}")
        
        if edge_disconnected_orig:
            print(f"    原始断开设备: {open_devices_orig}")
        if edge_disconnected_maint:
            print(f"    检修后断开设备: {open_devices_maint}")
        
        print()
    
    # 现在分析为什么会有两条"导致拆分的断开边"
    print("🏝️ 分析岛屿拆分的断开边:")
    print()
    
    # 读取岛屿变化分析结果
    with open("../out/new_islands_due_to_maintenance.json", "r", encoding="utf-8") as f:
        changes_data = json.load(f)
    
    split_edges = changes_data["changes"][0]["split_edges"]
    
    print(f"报告显示有 {len(split_edges)} 条导致拆分的断开边:")
    
    for i, split_edge in enumerate(split_edges, 1):
        from_name = split_edge["from_name"]
        to_name = split_edge["to_name"]
        open_devices = split_edge["open_devices"]
        closed_devices = split_edge["closed_devices"]
        
        print(f"\n{i}. {from_name} <-> {to_name}")
        print(f"   断开设备: {open_devices}")
        print(f"   闭合设备: {closed_devices}")
        
        # 检查这条边在原始拓扑中的状态
        edge_idx = split_edge["edge_index"]
        orig_edge = clean_data["edges"][edge_idx]
        
        print(f"   边索引: {edge_idx}")
        print(f"   原始状态分析:")
        
        orig_open_devices = []
        for dev in orig_edge.get("devices_status", []):
            if dev.get("status") == "OPEN":
                orig_open_devices.append(dev.get("name"))
        
        if orig_open_devices:
            print(f"     原始就断开的设备: {orig_open_devices}")
            print(f"     ❗ 这条边在原始拓扑中就是断开的！")
        else:
            print(f"     原始拓扑中此边是连通的")
    
    print("\n" + "="*60)
    print("🎯 结论分析:")
    print()
    
    print("您观察到的现象是正确的！原因如下：")
    print()
    print("1. 🔧 您只修改了一个设备的状态")
    print(f"   - 设备: {modified_device}")
    print(f"   - 状态变化: CLOSED -> OPEN")
    print()
    
    print("2. 🏝️ 但岛屿拆分分析显示了2条'导致拆分的断开边'")
    print("   这是因为算法的逻辑是：")
    print("   - 找到原本在同一个岛屿中的节点")
    print("   - 检修后这些节点分布在不同岛屿中")
    print("   - 算法会找出所有连接这些不同岛屿的断开边")
    print()
    
    print("3. ❗ 关键点：其中一条边在原始拓扑中就是断开的")
    print("   - 第2条边的断开设备在原始拓扑中状态就是OPEN")
    print("   - 这条边本来就不连通，但被算法识别为'导致拆分的边'")
    print("   - 这是算法逻辑上的一个问题")
    print()
    
    print("4. 🔍 真正导致拆分的只有您修改的那条边")
    print("   - 其他'导致拆分的边'实际上是原本就断开的")
    print("   - 算法需要改进以区分'新断开的边'和'原本就断开的边'")


if __name__ == "__main__":
    analyze_edge_disconnection()
