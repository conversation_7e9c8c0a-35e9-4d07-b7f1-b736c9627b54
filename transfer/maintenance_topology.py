import json


def validate_maintenance_update(original_path, maintenance_path, maintenance_devices):
    """
    验证检修状态更新的正确性
    """
    maintenance_names = set(dev["name"] for dev in maintenance_devices)

    with open(original_path, "r", encoding="utf-8") as f:
        original_data = json.load(f)

    with open(maintenance_path, "r", encoding="utf-8") as f:
        maintenance_data = json.load(f)

    print("\n=== 检修状态更新验证 ===")

    # 验证节点状态更新
    original_nodes = {n["id"]: n for n in original_data.get("nodes", [])}
    maintenance_nodes = {n["id"]: n for n in maintenance_data.get("nodes", [])}

    node_changes = 0
    for node_id, orig_node in original_nodes.items():
        maint_node = maintenance_nodes.get(node_id)
        if not maint_node:
            continue

        orig_status = orig_node.get("status")
        maint_status = maint_node.get("status")

        if orig_status != maint_status:
            node_name = maint_node.get("matched_name") or maint_node.get("name")
            print(f"节点状态变化: {node_name} ({orig_status} -> {maint_status})")
            node_changes += 1

    # 验证设备状态更新
    device_changes = 0
    for i, orig_edge in enumerate(original_data.get("edges", [])):
        maint_edge = maintenance_data.get("edges", [])[i] if i < len(maintenance_data.get("edges", [])) else None
        if not maint_edge:
            continue

        orig_devices = orig_edge.get("devices_status", [])
        maint_devices = maint_edge.get("devices_status", [])

        for j, orig_dev in enumerate(orig_devices):
            maint_dev = maint_devices[j] if j < len(maint_devices) else None
            if not maint_dev:
                continue

            orig_status = orig_dev.get("status")
            maint_status = maint_dev.get("status")

            if orig_status != maint_status:
                dev_name = maint_dev.get("matched_name") or maint_dev.get("name")
                print(f"设备状态变化: {dev_name} ({orig_status} -> {maint_status})")
                device_changes += 1

    print(f"\n验证结果:")
    print(f"- 节点状态变化数量: {node_changes}")
    print(f"- 设备状态变化数量: {device_changes}")

    # 检查是否有遗漏的检修设备
    missed_devices = []
    for edge in maintenance_data.get("edges", []):
        for dev in edge.get("devices_status", []):
            dev_name = dev.get("name")
            matched_name = dev.get("matched_name")
            if ((dev_name in maintenance_names or matched_name in maintenance_names)
                and dev.get("status") != "OPEN"):
                missed_devices.append(dev_name or matched_name)

    if missed_devices:
        print(f"\n⚠️  警告: 以下检修设备状态未正确更新:")
        for dev in missed_devices:
            print(f"  - {dev}")
    else:
        print(f"\n✅ 所有检修设备状态已正确更新")

    return node_changes, device_changes, len(missed_devices)


def apply_maintenance(clean_topology_path, output_path, maintenance_devices):
    """
    根据检修设备/节点修改拓扑边和节点状态，并生成新的 JSON
    maintenance_devices: [{"type": "Busbar"/"Winding"/"Load"/"Line", "name": "..."}]
    """
    # 构建检修设备/节点 name 集合
    maintenance_names = set(dev["name"] for dev in maintenance_devices)

    with open(clean_topology_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    # --- 修改节点状态 ---
    nodes = data.get("nodes", [])
    nodes_updated = 0
    for node in nodes:
        # 检查两个可能的名称字段
        node_name = node.get("name")
        matched_name = node.get("matched_name")

        # 如果任一名称匹配检修列表，则设置为检修状态
        if (node_name in maintenance_names) or (matched_name in maintenance_names):
            if node.get("status") != "OPEN":
                original_status = node.get("status", "UNKNOWN")
                node["status"] = "OPEN"
                display_name = matched_name or node_name
                print(f"节点 {display_name} 状态从 {original_status} 设置为检修状态 (OPEN)")
                nodes_updated += 1

    # --- 修改边上设备状态 ---
    edges = data.get("edges", [])
    devices_updated = 0
    for edge in edges:
        if not isinstance(edge, dict):
            continue
        devices_status = edge.get("devices_status", [])
        for dev in devices_status:
            # 检查两个可能的名称字段
            dev_name = dev.get("name")
            matched_name = dev.get("matched_name")

            # 如果任一名称匹配检修列表，则设置为检修状态
            if (dev_name in maintenance_names) or (matched_name in maintenance_names):
                if dev.get("status") != "OPEN":
                    original_status = dev.get("status", "UNKNOWN")
                    dev["status"] = "OPEN"
                    display_name = matched_name or dev_name
                    print(f"设备 {display_name} 状态从 {original_status} 设置为检修状态 (OPEN)")
                    devices_updated += 1

    # 保存新的拓扑 JSON
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    print(f"\n检修状态更新完成:")
    print(f"- 更新节点数量: {nodes_updated}")
    print(f"- 更新设备数量: {devices_updated}")
    print(f"- 已生成带检修状态的拓扑 JSON: {output_path}")


if __name__ == "__main__":
    # 示例检修设备/节点
    maintenance_devices = [
        {"name": "重庆.万州.城北站/10kV.901手车刀闸"}
    ]

    clean_path = "../out/clean_topology.json"
    maintenance_path = "../out/maintenance_topology.json"

    # 应用检修状态更新
    apply_maintenance(
        clean_topology_path=clean_path,
        output_path=maintenance_path,
        maintenance_devices=maintenance_devices
    )

    # 验证更新结果
    validate_maintenance_update(
        original_path=clean_path,
        maintenance_path=maintenance_path,
        maintenance_devices=maintenance_devices
    )
