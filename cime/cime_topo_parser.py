import json
import re
from collections import defaultdict, deque


class CIMEParser:
    def __init__(self, cime_file: str, encoding="gbk"):
        self.cime_file = cime_file
        self.encoding = encoding
        self.blocks = {}
        self.nodes = {}
        self.node_type_map = {}
        self.node_substation_map = {}
        self.node_voltage_map = {}
        self.node_transformer_map = {}
        self.i_j_map = defaultdict(list)
        self.edges = []
        self.substations = {}
        self.base_voltage_map = {}

    def parse_file(self):
        with open(self.cime_file, "r", encoding=self.encoding) as f:
            lines = [line.rstrip("\n") for line in f if line.strip()]

        current_tag = None
        header = []
        for line in lines:
            m_tag_start = re.match(r"<(\w+)>", line)
            m_tag_end = re.match(r"</(\w+)>", line)
            if m_tag_start:
                current_tag = m_tag_start.group(1)
                self.blocks[current_tag] = {"header": [], "data": []}
            elif m_tag_end:
                current_tag = None
            elif current_tag:
                if line.startswith("@"):
                    header = re.split(r"\t+", line[1:].strip())
                    self.blocks[current_tag]["header"] = header
                else:
                    values = re.split(r"\t+", line.lstrip("#").strip())
                    if len(values) < len(header):
                        values += [""] * (len(header) - len(values))
                    self.blocks[current_tag]["data"].append(dict(zip(header, values)))

    def parse_base_voltages(self):
        """解析 BaseVoltage 块，将 id -> 电压值 映射"""
        for bv in self.blocks.get("BaseVoltage", {}).get("data", []):
            bv_id = bv.get("mRID") or bv.get("id")
            kv = bv.get("name")
            if bv_id and kv:
                try:
                    self.base_voltage_map[bv_id] = kv
                except ValueError:
                    self.base_voltage_map[bv_id] = None

    def parse_substations(self):
        for ss in self.blocks.get("Substation", {}).get("data", []):
            ss_type = ss.get("type")
            if ss_type != "变电站":
                continue
            ss_id = ss.get("mRID") or ss.get("id") or ss.get("I_node")
            ss_name = ss.get("pathName") or ss.get("name") or "未知厂站"
            if ss_id:
                self.substations[ss_id] = ss_name

    def parse_nodes(self):
        for block_name, ntype in [("BusbarSection", "Busbar"),
                                  ("TransformerWinding", "Winding"),
                                  ("Load", "Load")]:
            for n in self.blocks.get(block_name, {}).get("data", []):
                # 电压等级
                bv_id = n.get("BaseVoltage")
                # 厂站
                ss_id = n.get("Substation")
                if not ss_id or ss_id not in self.substations or bv_id > "v_6":
                    continue
                node_id = n["I_node"]
                node_name = n.get("pathName") or n.get("name")
                self.nodes[node_id] = node_name
                self.node_type_map[node_id] = ntype
                self.node_substation_map[node_id] = self.substations[ss_id]
                voltage = self.base_voltage_map.get(bv_id)
                if voltage:
                    self.node_voltage_map[node_id] = voltage
                # 变压器ID（仅绕组）
                if ntype == "Winding":
                    transformer_id = n.get("PowerTransformer")  # CIME里可能是 Transformer 字段
                    if transformer_id:
                        self.node_transformer_map[node_id] = transformer_id

    def parse_devices(self):
        dual_end_tags = ["Breaker", "Disconnector"]
        for tag in dual_end_tags:
            for dev in self.blocks.get(tag, {}).get("data", []):
                # 电压等级
                bv_id = dev.get("BaseVoltage")
                if bv_id > "v_6":
                    continue
                i_node = dev.get("I_node")
                j_node = dev.get("J_node")
                name = dev.get("pathName") or tag
                if i_node and j_node:
                    self.i_j_map[i_node].append((name, j_node))
                    self.i_j_map[j_node].append((name, i_node))

    def parse_lines_into_map(self):
        """把 ACLineSegment 线路加入 i_j_map，保证 DFS 遍历顺序"""
        dot_map = {}
        dot_segment_map = {}
        # 解析线路的端点
        for dot in self.blocks.get("ACLineDot", {}).get("data", []):
            dot_id = dot.get("mRID")
            i_node = dot.get("I_node")
            seg_id = dot.get("ACLineSegment")
            if dot_id and i_node:
                dot_map[dot_id] = i_node
                dot_segment_map[dot_id] = seg_id

        for seg in self.blocks.get("ACLineSegment", {}).get("data", []):
            # 电压等级
            bv_id = seg.get("BaseVoltage")
            if bv_id > "v_6":
                continue
            seg_id = seg.get("mRID")
            line_name = seg.get("pathName") or seg.get("name") or "Line"
            # 找到线路的两端点
            dots = [d for d, sid in dot_segment_map.items() if sid == seg_id]
            if len(dots) != 2:
                continue
            n1 = dot_map[dots[0]]
            n2 = dot_map[dots[1]]
            # 把线路加入 i_j_map
            self.i_j_map[n1].append((line_name, n2))
            self.i_j_map[n2].append((line_name, n1))

    def find_path_to_major_node(self, start_node):
        visited = {start_node}
        queue = deque([(start_node, [])])
        while queue:
            current, path = queue.popleft()
            current_type = self.node_type_map.get(current)
            if current_type in ["Busbar", "Winding", "Load"]:
                return current, path
            for dev_name, neighbor in self.i_j_map.get(current, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [dev_name]))
        return None, []

    def build_edges(self):
        """BFS 构建母线/负荷/绕组之间的边，保持 devices 顺序 from->to，去重相同物理路径"""
        edge_set = set()  # 节点对 + 设备集合去重

        def make_edge_key(n1, n2, devices):
            return (tuple(sorted([n1, n2])), frozenset(devices))  # frozenset忽略顺序

        major_nodes = [nid for nid, ntype in self.node_type_map.items() if ntype in ["Busbar", "Load", "Winding"]]

        for start_node in major_nodes:
            queue = deque([(start_node, [], set([start_node]))])
            while queue:
                current, path, visited = queue.popleft()
                for dev_name, neighbor in self.i_j_map.get(current, []):
                    if neighbor in visited:
                        continue

                    new_path = path + [dev_name]
                    new_visited = visited | {neighbor}

                    if neighbor in major_nodes and neighbor != start_node:
                        key = make_edge_key(start_node, neighbor, new_path)
                        if key not in edge_set:
                            edge_set.add(key)
                            # 保留 BFS 首次遍历顺序作为展示顺序
                            self.edges.append({
                                "from": f"{start_node} [{self.nodes.get(start_node, '未知节点')}]",
                                "to": f"{neighbor} [{self.nodes.get(neighbor, '未知节点')}]",
                                "devices": new_path,
                                "from_voltage": self.node_voltage_map.get(start_node),
                                "to_voltage": self.node_voltage_map.get(neighbor),
                                "min_voltage": min(
                                    float(self.node_voltage_map.get(start_node, "0").replace("kV", "")),
                                    float(self.node_voltage_map.get(neighbor, "0").replace("kV", ""))
                                )
                            })
                        continue

                    queue.append((neighbor, new_path, new_visited))

    def build_transformer_edges(self):
        """根据电压等级连接同一个变压器的绕组"""
        from collections import defaultdict
        # 按变压器分组
        transformer_groups = defaultdict(list)
        for node_id, transformer_id in self.node_transformer_map.items():
            transformer_groups[transformer_id].append(node_id)
        for transformer_id, nodes in transformer_groups.items():
            # 按电压排序，高压->低压
            nodes_sorted = sorted(
                nodes,
                key=lambda nid: float(self.node_voltage_map.get(nid, "0").replace("kV", "")),
                reverse=True
            )
            for i in range(len(nodes_sorted) - 1):
                n1 = nodes_sorted[i]
                n2 = nodes_sorted[i + 1]
                self.edges.append({
                    "from": f"{n1} [{self.nodes[n1]}]",
                    "to": f"{n2} [{self.nodes[n2]}]",
                    "devices": [f"Transformer-{transformer_id}"],
                    "from_voltage": self.node_voltage_map.get(n1),
                    "to_voltage": self.node_voltage_map.get(n2),
                    "min_voltage": min(
                        float(self.node_voltage_map.get(n1, "0").replace("kV", "")),
                        float(self.node_voltage_map.get(n2, "0").replace("kV", ""))
                    )
                })

    def to_json(self):
        return {
            "nodes": [
                {
                    "id": nid,
                    "name": nname,
                    "type": self.node_type_map.get(nid),
                    "substation": self.node_substation_map.get(nid, "未知厂站"),
                    "voltage": self.node_voltage_map.get(nid)
                }
                for nid, nname in self.nodes.items()
            ],
            "edges": self.edges
        }

    def save_json(self, path):
        with open(path, "w", encoding="utf-8") as f:
            json.dump(self.to_json(), f, ensure_ascii=False, indent=2)

    def save_single_node_edges(self, path):
        """
        保存真正的单节点（孤立节点）及其连接设备信息
        条件：节点没有任何边连接到其他节点
        """
        single_node_edges = []

        # 先找所有连通节点集合
        connected_nodes = set()
        for edge in self.edges:
            # 从 edges 里提取节点 ID（去掉 name）
            n1 = edge["from"].split(" ")[0]
            n2 = edge["to"].split(" ")[0]
            connected_nodes.add(n1)
            connected_nodes.add(n2)

        # 遍历所有节点
        for node_id, node_name in self.nodes.items():
            if node_id not in connected_nodes:
                # 节点没有任何连通边，是孤立节点
                devices = [dev_name for dev_name, _ in self.i_j_map.get(node_id, [])]
                single_node_edges.append({
                    "node_id": node_id,
                    "node_name": node_name,
                    "devices": devices,
                    "voltage": self.node_voltage_map.get(node_id),
                    "substation": self.node_substation_map.get(node_id, "未知厂站")
                })

        with open(path, "w", encoding="utf-8") as f:
            json.dump(single_node_edges, f, ensure_ascii=False, indent=2)

        print(f"已保存 {len(single_node_edges)} 个单节点的设备信息到 {path}")

    def run(self, output_path_full: str, output_path_single: str):
        """统一执行解析流程并保存 JSON"""
        self.parse_file()
        self.parse_base_voltages()
        self.parse_substations()
        self.parse_nodes()
        self.parse_devices()
        self.parse_lines_into_map()
        self.build_edges()
        self.build_transformer_edges()
        # 保存完整拓扑
        self.save_json(output_path_full)
        # 保存单节点设备信息
        self.save_single_node_edges(output_path_single)
