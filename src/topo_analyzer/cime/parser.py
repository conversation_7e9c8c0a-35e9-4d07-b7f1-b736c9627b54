"""
CIME文件解析器

用于解析CIME格式的电力系统拓扑文件，提取节点、设备和连接关系
"""

import json
import re
from collections import defaultdict, deque
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

from ..config.logger_config import get_logger
from ..config.settings import settings

logger = get_logger(__name__)


class CIMEParseError(Exception):
    """CIME解析异常"""
    pass


class CIMEParser:
    """
    CIME文件解析器
    
    解析CIME格式的电力系统拓扑文件，提取节点、设备和连接关系，
    构建电力系统拓扑图
    """
    
    def __init__(self, cime_file: str, encoding: Optional[str] = None):
        """
        初始化CIME解析器
        
        Args:
            cime_file: CIME文件路径
            encoding: 文件编码，默认使用配置中的编码
        """
        self.cime_file = Path(cime_file)
        self.encoding = encoding or settings.processing.cime_encoding
        
        # 验证文件存在
        if not self.cime_file.exists():
            raise FileNotFoundError(f"CIME文件不存在: {cime_file}")
        
        # 初始化数据结构
        self.blocks: Dict[str, Dict[str, Any]] = {}
        self.nodes: Dict[str, str] = {}
        self.node_type_map: Dict[str, str] = {}
        self.node_substation_map: Dict[str, str] = {}
        self.node_voltage_map: Dict[str, str] = {}
        self.node_transformer_map: Dict[str, str] = {}
        self.i_j_map: Dict[str, List[Tuple[str, str]]] = defaultdict(list)
        self.edges: List[Dict[str, Any]] = []
        self.substations: Dict[str, str] = {}
        self.base_voltage_map: Dict[str, str] = {}
        
        logger.info(f"初始化CIME解析器，文件: {self.cime_file}")
    
    def parse_file(self) -> None:
        """解析CIME文件，提取所有标签块"""
        try:
            with open(self.cime_file, "r", encoding=self.encoding) as f:
                lines = [line.rstrip("\n") for line in f if line.strip()]
            
            logger.info(f"读取CIME文件，共 {len(lines)} 行")
            
            current_tag = None
            header = []
            
            for line_num, line in enumerate(lines, 1):
                try:
                    # 匹配标签开始
                    m_tag_start = re.match(r"<(\w+)>", line)
                    if m_tag_start:
                        current_tag = m_tag_start.group(1)
                        self.blocks[current_tag] = {"header": [], "data": []}
                        continue
                    
                    # 匹配标签结束
                    m_tag_end = re.match(r"</(\w+)>", line)
                    if m_tag_end:
                        current_tag = None
                        continue
                    
                    # 处理标签内容
                    if current_tag:
                        if line.startswith("@"):
                            # 解析表头
                            header = re.split(r"\t+", line[1:].strip())
                            self.blocks[current_tag]["header"] = header
                        else:
                            # 解析数据行
                            values = re.split(r"\t+", line.lstrip("#").strip())
                            if len(values) < len(header):
                                values += [""] * (len(header) - len(values))
                            self.blocks[current_tag]["data"].append(dict(zip(header, values)))
                
                except Exception as e:
                    logger.warning(f"解析第 {line_num} 行时出错: {e}")
                    continue
            
            logger.info(f"解析完成，提取到 {len(self.blocks)} 个标签块")
            
        except Exception as e:
            raise CIMEParseError(f"解析CIME文件失败: {e}")
    
    def parse_base_voltages(self) -> None:
        """解析BaseVoltage块，建立电压等级映射"""
        base_voltage_data = self.blocks.get("BaseVoltage", {}).get("data", [])
        logger.info(f"解析BaseVoltage块，共 {len(base_voltage_data)} 条记录")
        
        for bv in base_voltage_data:
            bv_id = bv.get("mRID") or bv.get("id")
            kv = bv.get("name")
            if bv_id and kv:
                self.base_voltage_map[bv_id] = kv
        
        logger.info(f"建立电压等级映射，共 {len(self.base_voltage_map)} 个等级")
    
    def parse_substations(self) -> None:
        """解析Substation块，建立变电站映射"""
        substation_data = self.blocks.get("Substation", {}).get("data", [])
        logger.info(f"解析Substation块，共 {len(substation_data)} 条记录")
        
        for ss in substation_data:
            ss_type = ss.get("type")
            if ss_type != "变电站":
                continue
            
            ss_id = ss.get("mRID") or ss.get("id") or ss.get("I_node")
            ss_name = ss.get("pathName") or ss.get("name") or "未知厂站"
            
            if ss_id:
                self.substations[ss_id] = ss_name
        
        logger.info(f"建立变电站映射，共 {len(self.substations)} 个变电站")
    
    def parse_nodes(self) -> None:
        """解析节点信息"""
        node_blocks = [
            ("BusbarSection", "Busbar"),
            ("TransformerWinding", "Winding"),
            ("Load", "Load")
        ]
        
        total_nodes = 0
        for block_name, ntype in node_blocks:
            block_data = self.blocks.get(block_name, {}).get("data", [])
            block_nodes = 0
            
            for n in block_data:
                try:
                    # 电压等级检查
                    bv_id = n.get("BaseVoltage")
                    if bv_id and bv_id > "v_6":  # 过滤高电压等级
                        continue
                    
                    # 厂站检查
                    ss_id = n.get("Substation")
                    if not ss_id or ss_id not in self.substations:
                        continue
                    
                    node_id = n.get("I_node")
                    if not node_id:
                        continue
                    
                    node_name = n.get("pathName") or n.get("name")
                    if not node_name:
                        continue
                    
                    # 保存节点信息
                    self.nodes[node_id] = node_name
                    self.node_type_map[node_id] = ntype
                    self.node_substation_map[node_id] = self.substations[ss_id]
                    
                    # 电压等级
                    voltage = self.base_voltage_map.get(bv_id)
                    if voltage:
                        self.node_voltage_map[node_id] = voltage
                    
                    # 变压器ID（仅绕组）
                    if ntype == "Winding":
                        transformer_id = n.get("PowerTransformer")
                        if transformer_id:
                            self.node_transformer_map[node_id] = transformer_id
                    
                    block_nodes += 1
                    
                except Exception as e:
                    logger.warning(f"解析节点时出错: {e}")
                    continue
            
            total_nodes += block_nodes
            logger.info(f"解析 {block_name} 节点: {block_nodes} 个")
        
        logger.info(f"节点解析完成，共 {total_nodes} 个节点")
    
    def parse_devices(self) -> None:
        """解析设备信息"""
        dual_end_tags = ["Breaker", "Disconnector"]
        total_devices = 0
        
        for tag in dual_end_tags:
            device_data = self.blocks.get(tag, {}).get("data", [])
            tag_devices = 0
            
            for dev in device_data:
                try:
                    # 电压等级检查
                    bv_id = dev.get("BaseVoltage")
                    if bv_id and bv_id > "v_6":
                        continue
                    
                    i_node = dev.get("I_node")
                    j_node = dev.get("J_node")
                    name = dev.get("pathName") or tag
                    
                    if i_node and j_node:
                        self.i_j_map[i_node].append((name, j_node))
                        self.i_j_map[j_node].append((name, i_node))
                        tag_devices += 1
                
                except Exception as e:
                    logger.warning(f"解析设备时出错: {e}")
                    continue
            
            total_devices += tag_devices
            logger.info(f"解析 {tag} 设备: {tag_devices} 个")
        
        logger.info(f"设备解析完成，共 {total_devices} 个设备")
    
    def parse_lines_into_map(self) -> None:
        """解析线路信息并加入连接映射"""
        # 解析线路端点
        dot_map = {}
        dot_segment_map = {}
        
        dot_data = self.blocks.get("ACLineDot", {}).get("data", [])
        for dot in dot_data:
            dot_id = dot.get("mRID")
            i_node = dot.get("I_node")
            seg_id = dot.get("ACLineSegment")
            if dot_id and i_node:
                dot_map[dot_id] = i_node
                dot_segment_map[dot_id] = seg_id
        
        # 解析线路段
        segment_data = self.blocks.get("ACLineSegment", {}).get("data", [])
        lines_added = 0
        
        for seg in segment_data:
            try:
                # 电压等级检查
                bv_id = seg.get("BaseVoltage")
                if bv_id and bv_id > "v_6":
                    continue
                
                seg_id = seg.get("mRID")
                line_name = seg.get("pathName") or seg.get("name") or "Line"
                
                # 找到线路的两端点
                dots = [d for d, sid in dot_segment_map.items() if sid == seg_id]
                if len(dots) != 2:
                    continue
                
                n1 = dot_map[dots[0]]
                n2 = dot_map[dots[1]]
                
                # 加入连接映射
                self.i_j_map[n1].append((line_name, n2))
                self.i_j_map[n2].append((line_name, n1))
                lines_added += 1
                
            except Exception as e:
                logger.warning(f"解析线路时出错: {e}")
                continue
        
        logger.info(f"线路解析完成，共 {lines_added} 条线路")
    
    def find_path_to_major_node(self, start_node: str) -> Tuple[Optional[str], List[str]]:
        """
        从起始节点找到最近的主要节点（母线、绕组、负荷）
        
        Args:
            start_node: 起始节点ID
            
        Returns:
            (目标节点ID, 路径上的设备列表)
        """
        visited = {start_node}
        queue = deque([(start_node, [])])
        
        while queue:
            current, path = queue.popleft()
            current_type = self.node_type_map.get(current)
            
            if current_type in ["Busbar", "Winding", "Load"]:
                return current, path
            
            for dev_name, neighbor in self.i_j_map.get(current, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [dev_name]))
        
        return None, []

    def build_edges(self) -> None:
        """构建节点间的边，使用BFS遍历"""
        edge_set = set()  # 用于去重

        def make_edge_key(n1: str, n2: str, devices: List[str]) -> Tuple:
            """生成边的唯一标识"""
            return (tuple(sorted([n1, n2])), frozenset(devices))

        major_nodes = [
            nid for nid, ntype in self.node_type_map.items()
            if ntype in ["Busbar", "Load", "Winding"]
        ]

        logger.info(f"开始构建边，主要节点数: {len(major_nodes)}")

        for start_node in major_nodes:
            queue = deque([(start_node, [], {start_node})])

            while queue:
                current, path, visited = queue.popleft()

                for dev_name, neighbor in self.i_j_map.get(current, []):
                    if neighbor in visited:
                        continue

                    new_path = path + [dev_name]
                    new_visited = visited | {neighbor}

                    # 如果到达另一个主要节点
                    if neighbor in major_nodes and neighbor != start_node:
                        key = make_edge_key(start_node, neighbor, new_path)
                        if key not in edge_set:
                            edge_set.add(key)

                            # 计算最小电压
                            start_voltage = self.node_voltage_map.get(start_node, "0kV")
                            neighbor_voltage = self.node_voltage_map.get(neighbor, "0kV")

                            try:
                                start_kv = float(start_voltage.replace("kV", ""))
                                neighbor_kv = float(neighbor_voltage.replace("kV", ""))
                                min_voltage = min(start_kv, neighbor_kv)
                            except (ValueError, AttributeError):
                                min_voltage = 0.0

                            self.edges.append({
                                "from": f"{start_node} [{self.nodes.get(start_node, '未知节点')}]",
                                "to": f"{neighbor} [{self.nodes.get(neighbor, '未知节点')}]",
                                "devices": new_path,
                                "from_voltage": start_voltage,
                                "to_voltage": neighbor_voltage,
                                "min_voltage": min_voltage
                            })
                        continue

                    queue.append((neighbor, new_path, new_visited))

        logger.info(f"边构建完成，共 {len(self.edges)} 条边")

    def build_transformer_edges(self) -> None:
        """构建变压器绕组间的连接"""
        # 按变压器分组
        transformer_groups = defaultdict(list)
        for node_id, transformer_id in self.node_transformer_map.items():
            transformer_groups[transformer_id].append(node_id)

        transformer_edges = 0
        for transformer_id, nodes in transformer_groups.items():
            if len(nodes) < 2:
                continue

            # 按电压排序，高压->低压
            try:
                nodes_sorted = sorted(
                    nodes,
                    key=lambda nid: float(
                        self.node_voltage_map.get(nid, "0kV").replace("kV", "")
                    ),
                    reverse=True
                )
            except (ValueError, AttributeError):
                nodes_sorted = nodes

            # 连接相邻电压等级的绕组
            for i in range(len(nodes_sorted) - 1):
                n1 = nodes_sorted[i]
                n2 = nodes_sorted[i + 1]

                # 计算最小电压
                try:
                    n1_kv = float(self.node_voltage_map.get(n1, "0kV").replace("kV", ""))
                    n2_kv = float(self.node_voltage_map.get(n2, "0kV").replace("kV", ""))
                    min_voltage = min(n1_kv, n2_kv)
                except (ValueError, AttributeError):
                    min_voltage = 0.0

                self.edges.append({
                    "from": f"{n1} [{self.nodes[n1]}]",
                    "to": f"{n2} [{self.nodes[n2]}]",
                    "devices": [f"Transformer-{transformer_id}"],
                    "from_voltage": self.node_voltage_map.get(n1),
                    "to_voltage": self.node_voltage_map.get(n2),
                    "min_voltage": min_voltage
                })
                transformer_edges += 1

        logger.info(f"变压器边构建完成，共 {transformer_edges} 条变压器边")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "nodes": [
                {
                    "id": nid,
                    "name": nname,
                    "type": self.node_type_map.get(nid),
                    "substation": self.node_substation_map.get(nid, "未知厂站"),
                    "voltage": self.node_voltage_map.get(nid)
                }
                for nid, nname in self.nodes.items()
            ],
            "edges": self.edges
        }

    def save_json(self, path: str) -> None:
        """保存为JSON文件"""
        output_path = Path(path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)

        logger.info(f"拓扑数据已保存到: {output_path}")

    def save_single_node_edges(self, path: str) -> None:
        """保存孤立节点信息"""
        # 找出所有连通的节点
        connected_nodes = set()
        for edge in self.edges:
            n1 = edge["from"].split(" ")[0]
            n2 = edge["to"].split(" ")[0]
            connected_nodes.add(n1)
            connected_nodes.add(n2)

        # 找出孤立节点
        single_node_edges = []
        for node_id, node_name in self.nodes.items():
            if node_id not in connected_nodes:
                devices = [dev_name for dev_name, _ in self.i_j_map.get(node_id, [])]
                single_node_edges.append({
                    "node_id": node_id,
                    "node_name": node_name,
                    "devices": devices,
                    "voltage": self.node_voltage_map.get(node_id),
                    "substation": self.node_substation_map.get(node_id, "未知厂站")
                })

        output_path = Path(path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(single_node_edges, f, ensure_ascii=False, indent=2)

        logger.info(f"孤立节点数据已保存到: {output_path}，共 {len(single_node_edges)} 个节点")

    def run(self, output_path_full: str, output_path_single: str) -> Dict[str, Any]:
        """
        执行完整的解析流程

        Args:
            output_path_full: 完整拓扑输出路径
            output_path_single: 孤立节点输出路径

        Returns:
            解析结果字典
        """
        logger.info("开始CIME文件解析流程")

        try:
            # 执行解析步骤
            self.parse_file()
            self.parse_base_voltages()
            self.parse_substations()
            self.parse_nodes()
            self.parse_devices()
            self.parse_lines_into_map()
            self.build_edges()
            self.build_transformer_edges()

            # 保存结果
            self.save_json(output_path_full)
            self.save_single_node_edges(output_path_single)

            result = self.to_dict()
            logger.info(f"CIME解析完成，节点数: {len(result['nodes'])}，边数: {len(result['edges'])}")

            return result

        except Exception as e:
            logger.error(f"CIME解析失败: {e}")
            raise CIMEParseError(f"CIME解析失败: {e}")
