"""
主程序模块

提供拓扑分析的主要业务逻辑和工作流程
"""

import concurrent.futures
import time
from pathlib import Path
from typing import Dict, Any, Optional

from .cime.parser import CIMEParser
from .qs.status_parser import QSStatusParser
from .qs.topology_matcher import TopologyMatcher
from .qs.topology_cleaner import TopologyCleaner
from .transfer.island_analyzer import run_island_partition
from .config.settings import Settings
from .config.logger_config import get_logger

logger = get_logger(__name__)


class TopologyAnalyzer:
    """
    拓扑分析器主类
    
    协调各个模块完成完整的拓扑分析流程
    """
    
    def __init__(self, settings: Optional[Settings] = None):
        """
        初始化拓扑分析器
        
        Args:
            settings: 配置对象，如果为None则使用默认配置
        """
        self.settings = settings or Settings()
        self.settings.ensure_directories()
        
        # 分析结果
        self.cime_result: Optional[Dict[str, Any]] = None
        self.qs_result: Optional[Dict[str, Any]] = None
        self.match_result: Optional[Dict[str, Any]] = None
        self.clean_result: Optional[Dict[str, Any]] = None
        self.island_result: Optional[bool] = None
        
        logger.info("拓扑分析器初始化完成")
    
    def parse_files_parallel(
        self, 
        cime_file: Optional[str] = None, 
        qs_file: Optional[str] = None
    ) -> tuple:
        """
        并行解析CIME和QS文件
        
        Args:
            cime_file: CIME文件路径，如果为None则使用配置中的路径
            qs_file: QS文件路径，如果为None则使用配置中的路径
            
        Returns:
            (cime_result, qs_result) 元组
        """
        cime_path = cime_file or self.settings.file_paths.cime_file
        qs_path = qs_file or self.settings.file_paths.qs_file
        
        if not cime_path or not qs_path:
            raise ValueError("必须指定CIME和QS文件路径")
        
        logger.info("开始并行解析CIME和QS文件")
        
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=self.settings.processing.max_workers
        ) as executor:
            # 提交解析任务
            cime_future = executor.submit(self._parse_cime, cime_path)
            qs_future = executor.submit(self._parse_qs, qs_path)
            
            # 获取结果
            self.cime_result = cime_future.result()
            self.qs_result = qs_future.result()
        
        logger.info("文件解析完成")
        return self.cime_result, self.qs_result
    
    def _parse_cime(self, cime_file: str) -> Dict[str, Any]:
        """解析CIME文件"""
        logger.info(f"解析CIME文件: {cime_file}")
        
        parser = CIMEParser(cime_file, self.settings.processing.cime_encoding)
        result = parser.run(
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.cime_topology_file)),
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.single_node_edges_file))
        )
        
        logger.info(f"CIME解析完成: {len(result['nodes'])} 节点, {len(result['edges'])} 边")
        return result
    
    def _parse_qs(self, qs_file: str) -> Dict[str, Any]:
        """解析QS文件"""
        logger.info(f"解析QS文件: {qs_file}")
        
        parser = QSStatusParser(qs_file, self.settings.processing.qs_encoding)
        result = parser.run(
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.qs_status_file))
        )
        
        logger.info(f"QS解析完成: {len(result['nodes'])} 节点状态, {len(result['devices'])} 设备状态")
        return result
    
    def match_topology(self) -> Dict[str, Any]:
        """匹配拓扑和状态"""
        if not self.cime_result or not self.qs_result:
            raise RuntimeError("请先解析CIME和QS文件")
        
        logger.info("开始拓扑匹配")
        
        matcher = TopologyMatcher(
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.cime_topology_file)),
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.qs_status_file)),
            self.settings.processing.match_threshold
        )
        
        self.match_result = matcher.run(
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.matched_output)),
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.unmatched_output))
        )
        
        logger.info(
            f"拓扑匹配完成: 节点匹配率 {self.match_result['node_match_rate']:.2%}, "
            f"设备匹配率 {self.match_result['device_match_rate']:.2%}"
        )
        
        return self.match_result
    
    def clean_topology(self) -> Dict[str, Any]:
        """清洗拓扑"""
        if not self.match_result:
            raise RuntimeError("请先进行拓扑匹配")
        
        logger.info("开始拓扑清洗")
        
        cleaner = TopologyCleaner(
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.matched_output))
        )
        
        self.clean_result = cleaner.run(
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.clean_output))
        )
        
        logger.info(
            f"拓扑清洗完成: 保留 {self.clean_result['clean_counts']['nodes']} 节点, "
            f"{self.clean_result['clean_counts']['edges']} 边"
        )
        
        return self.clean_result
    
    def analyze_islands(self, consider_status: Optional[bool] = None) -> bool:
        """分析岛屿"""
        if not self.clean_result:
            raise RuntimeError("请先进行拓扑清洗")
        
        consider_status = consider_status if consider_status is not None else self.settings.processing.consider_status
        
        logger.info(f"开始岛屿分析，考虑状态: {consider_status}")
        
        self.island_result = run_island_partition(
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.clean_output)),
            str(self.settings.file_paths.get_full_path(self.settings.file_paths.island_output)),
            consider_status
        )
        
        if self.island_result:
            logger.info("岛屿分析完成")
        else:
            logger.error("岛屿分析失败")
        
        return self.island_result
    
    def run_complete_analysis(
        self,
        cime_file: Optional[str] = None,
        qs_file: Optional[str] = None,
        consider_status: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        运行完整的分析流程
        
        Args:
            cime_file: CIME文件路径
            qs_file: QS文件路径
            consider_status: 岛屿分析是否考虑状态
            
        Returns:
            分析结果汇总
        """
        start_time = time.time()
        logger.info("开始完整拓扑分析流程")
        
        try:
            # 1. 并行解析文件
            self.parse_files_parallel(cime_file, qs_file)
            
            # 2. 拓扑匹配
            self.match_topology()
            
            # 3. 拓扑清洗
            self.clean_topology()
            
            # 4. 岛屿分析
            self.analyze_islands(consider_status)
            
            elapsed_time = time.time() - start_time
            
            # 生成分析报告
            report = {
                "success": True,
                "elapsed_time": elapsed_time,
                "cime_analysis": {
                    "nodes": len(self.cime_result['nodes']),
                    "edges": len(self.cime_result['edges'])
                },
                "qs_analysis": {
                    "node_status": len(self.qs_result['nodes']),
                    "device_status": len(self.qs_result['devices'])
                },
                "topology_matching": {
                    "node_match_rate": self.match_result['node_match_rate'],
                    "device_match_rate": self.match_result['device_match_rate']
                },
                "topology_cleaning": {
                    "retained_nodes": self.clean_result['clean_counts']['nodes'],
                    "retained_edges": self.clean_result['clean_counts']['edges'],
                    "removed_edges": self.clean_result['removed_counts']['edges']
                },
                "island_analysis": {
                    "success": self.island_result
                },
                "output_files": {
                    "cime_topology": str(self.settings.file_paths.get_full_path(self.settings.file_paths.cime_topology_file)),
                    "qs_status": str(self.settings.file_paths.get_full_path(self.settings.file_paths.qs_status_file)),
                    "matched_topology": str(self.settings.file_paths.get_full_path(self.settings.file_paths.matched_output)),
                    "clean_topology": str(self.settings.file_paths.get_full_path(self.settings.file_paths.clean_output)),
                    "island_analysis": str(self.settings.file_paths.get_full_path(self.settings.file_paths.island_output))
                }
            }
            
            logger.info(f"完整分析流程完成，总耗时: {elapsed_time:.2f} 秒")
            return report
            
        except Exception as e:
            logger.error(f"分析流程失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "elapsed_time": time.time() - start_time
            }
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取分析结果摘要"""
        if not all([self.cime_result, self.qs_result, self.match_result, self.clean_result]):
            return {"error": "分析尚未完成"}
        
        return {
            "cime_nodes": len(self.cime_result['nodes']),
            "cime_edges": len(self.cime_result['edges']),
            "qs_node_status": len(self.qs_result['nodes']),
            "qs_device_status": len(self.qs_result['devices']),
            "node_match_rate": self.match_result['node_match_rate'],
            "device_match_rate": self.match_result['device_match_rate'],
            "clean_nodes": self.clean_result['clean_counts']['nodes'],
            "clean_edges": self.clean_result['clean_counts']['edges'],
            "island_analysis_success": self.island_result
        }


def main():
    """主函数 - 兼容原有的main.py调用方式"""
    # 使用默认配置运行分析
    settings = Settings()
    
    # 从配置中获取文件路径
    cime_file = settings.file_paths.cime_file
    qs_file = settings.file_paths.qs_file
    
    if not cime_file or not qs_file:
        logger.error("请在配置文件中指定CIME和QS文件路径")
        return
    
    # 创建分析器并运行
    analyzer = TopologyAnalyzer(settings)
    result = analyzer.run_complete_analysis()
    
    if result['success']:
        logger.info("分析完成！")
        summary = analyzer.get_analysis_summary()
        for key, value in summary.items():
            logger.info(f"{key}: {value}")
    else:
        logger.error(f"分析失败: {result.get('error', '未知错误')}")


if __name__ == "__main__":
    main()
