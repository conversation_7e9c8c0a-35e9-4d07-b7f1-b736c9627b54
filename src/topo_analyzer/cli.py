"""
命令行接口

提供拓扑分析器的命令行工具
"""

import concurrent.futures
import time
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

from .cime.parser import CIMEParser
from .qs.status_parser import QSStatusParser
from .qs.topology_matcher import TopologyMatcher
from .qs.topology_cleaner import TopologyCleaner
from .transfer.island_analyzer import IslandAnalyzer, run_island_partition
from .config.settings import Settings
from .config.logger_config import get_logger

console = Console()
logger = get_logger(__name__)


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
@click.pass_context
def cli(ctx, config, verbose):
    """电力系统拓扑分析器"""
    ctx.ensure_object(dict)
    
    # 加载配置
    if config:
        ctx.obj['settings'] = Settings.from_yaml(config)
    else:
        ctx.obj['settings'] = Settings()
    
    # 设置日志级别
    if verbose:
        ctx.obj['settings'].logging.level = "DEBUG"
    
    # 确保目录存在
    ctx.obj['settings'].ensure_directories()


@cli.command()
@click.option('--cime-file', '-c', type=click.Path(exists=True), help='CIME文件路径')
@click.option('--output', '-o', type=click.Path(), help='输出文件路径')
@click.option('--single-output', '-s', type=click.Path(), help='单节点输出文件路径')
@click.pass_context
def parse_cime(ctx, cime_file, output, single_output):
    """解析CIME文件"""
    settings = ctx.obj['settings']
    
    # 使用命令行参数或配置文件中的路径
    cime_path = cime_file or settings.file_paths.cime_file
    output_path = output or settings.file_paths.get_full_path(settings.file_paths.cime_topology_file)
    single_path = single_output or settings.file_paths.get_full_path(settings.file_paths.single_node_edges_file)
    
    if not cime_path:
        console.print("[red]错误: 请指定CIME文件路径[/red]")
        return
    
    console.print(f"[blue]解析CIME文件: {cime_path}[/blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("解析中...", total=None)
        
        try:
            parser = CIMEParser(cime_path)
            result = parser.run(str(output_path), str(single_path))
            
            progress.update(task, description="完成")
            
            # 显示结果
            table = Table(title="CIME解析结果")
            table.add_column("项目", style="cyan")
            table.add_column("数量", style="green")
            
            table.add_row("节点数", str(len(result['nodes'])))
            table.add_row("边数", str(len(result['edges'])))
            table.add_row("输出文件", str(output_path))
            table.add_row("单节点文件", str(single_path))
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]解析失败: {e}[/red]")


@cli.command()
@click.option('--qs-file', '-q', type=click.Path(exists=True), help='QS文件路径')
@click.option('--output', '-o', type=click.Path(), help='输出文件路径')
@click.pass_context
def parse_qs(ctx, qs_file, output):
    """解析QS状态文件"""
    settings = ctx.obj['settings']
    
    # 使用命令行参数或配置文件中的路径
    qs_path = qs_file or settings.file_paths.qs_file
    output_path = output or settings.file_paths.get_full_path(settings.file_paths.qs_status_file)
    
    if not qs_path:
        console.print("[red]错误: 请指定QS文件路径[/red]")
        return
    
    console.print(f"[blue]解析QS文件: {qs_path}[/blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("解析中...", total=None)
        
        try:
            parser = QSStatusParser(qs_path)
            result = parser.run(str(output_path))
            
            progress.update(task, description="完成")
            
            # 显示结果
            table = Table(title="QS解析结果")
            table.add_column("项目", style="cyan")
            table.add_column("数量", style="green")
            
            table.add_row("节点状态", str(len(result['nodes'])))
            table.add_row("设备状态", str(len(result['devices'])))
            table.add_row("输出文件", str(output_path))
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]解析失败: {e}[/red]")


@cli.command()
@click.option('--topo-file', '-t', type=click.Path(exists=True), help='拓扑文件路径')
@click.option('--status-file', '-s', type=click.Path(exists=True), help='状态文件路径')
@click.option('--output', '-o', type=click.Path(), help='匹配结果输出路径')
@click.option('--unmatched', '-u', type=click.Path(), help='未匹配元素输出路径')
@click.option('--threshold', type=int, help='匹配阈值')
@click.pass_context
def match_topology(ctx, topo_file, status_file, output, unmatched, threshold):
    """匹配拓扑和状态"""
    settings = ctx.obj['settings']
    
    # 使用命令行参数或配置文件中的路径
    topo_path = topo_file or settings.file_paths.get_full_path(settings.file_paths.cime_topology_file)
    status_path = status_file or settings.file_paths.get_full_path(settings.file_paths.qs_status_file)
    output_path = output or settings.file_paths.get_full_path(settings.file_paths.matched_output)
    unmatched_path = unmatched or settings.file_paths.get_full_path(settings.file_paths.unmatched_output)
    match_threshold = threshold or settings.processing.match_threshold
    
    console.print(f"[blue]匹配拓扑和状态，阈值: {match_threshold}[/blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("匹配中...", total=None)
        
        try:
            matcher = TopologyMatcher(str(topo_path), str(status_path), match_threshold)
            result = matcher.run(str(output_path), str(unmatched_path))
            
            progress.update(task, description="完成")
            
            # 显示结果
            table = Table(title="拓扑匹配结果")
            table.add_column("项目", style="cyan")
            table.add_column("数量/比率", style="green")
            
            table.add_row("总节点数", str(result['total_nodes']))
            table.add_row("匹配节点数", str(result['matched_nodes']))
            table.add_row("节点匹配率", f"{result['node_match_rate']:.2%}")
            table.add_row("总设备数", str(result['total_devices']))
            table.add_row("匹配设备数", str(result['matched_devices']))
            table.add_row("设备匹配率", f"{result['device_match_rate']:.2%}")
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]匹配失败: {e}[/red]")


@cli.command()
@click.option('--input', '-i', type=click.Path(exists=True), help='输入拓扑文件路径')
@click.option('--output', '-o', type=click.Path(), help='清洗后输出路径')
@click.pass_context
def clean_topology(ctx, input, output):
    """清洗拓扑数据"""
    settings = ctx.obj['settings']
    
    # 使用命令行参数或配置文件中的路径
    input_path = input or settings.file_paths.get_full_path(settings.file_paths.matched_output)
    output_path = output or settings.file_paths.get_full_path(settings.file_paths.clean_output)
    
    console.print(f"[blue]清洗拓扑数据[/blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("清洗中...", total=None)
        
        try:
            cleaner = TopologyCleaner(str(input_path))
            result = cleaner.run(str(output_path))
            
            progress.update(task, description="完成")
            
            # 显示结果
            table = Table(title="拓扑清洗结果")
            table.add_column("项目", style="cyan")
            table.add_column("原始", style="yellow")
            table.add_column("清洗后", style="green")
            
            table.add_row("节点数", str(result['original_counts']['nodes']), str(result['clean_counts']['nodes']))
            table.add_row("边数", str(result['original_counts']['edges']), str(result['clean_counts']['edges']))
            table.add_row("移除边数", "-", str(result['removed_counts']['edges']))
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]清洗失败: {e}[/red]")


@cli.command()
@click.option('--input', '-i', type=click.Path(exists=True), help='输入拓扑文件路径')
@click.option('--output', '-o', type=click.Path(), help='岛屿分析输出路径')
@click.option('--consider-status/--ignore-status', default=False, help='是否考虑设备状态')
@click.pass_context
def analyze_islands(ctx, input, output, consider_status):
    """分析拓扑岛屿"""
    settings = ctx.obj['settings']
    
    # 使用命令行参数或配置文件中的路径
    input_path = input or settings.file_paths.get_full_path(settings.file_paths.clean_output)
    output_path = output or settings.file_paths.get_full_path(settings.file_paths.island_output)
    
    console.print(f"[blue]分析拓扑岛屿，考虑状态: {consider_status}[/blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("分析中...", total=None)
        
        try:
            success = run_island_partition(str(input_path), str(output_path), consider_status)
            
            if success:
                progress.update(task, description="完成")
                console.print(f"[green]岛屿分析完成，结果保存到: {output_path}[/green]")
            else:
                console.print("[red]岛屿分析失败[/red]")
            
        except Exception as e:
            console.print(f"[red]分析失败: {e}[/red]")


@cli.command()
@click.option('--cime-file', '-c', type=click.Path(exists=True), help='CIME文件路径')
@click.option('--qs-file', '-q', type=click.Path(exists=True), help='QS文件路径')
@click.option('--output-dir', '-o', type=click.Path(), help='输出目录')
@click.option('--threshold', type=int, help='匹配阈值')
@click.option('--consider-status/--ignore-status', default=False, help='岛屿分析是否考虑设备状态')
@click.pass_context
def run_all(ctx, cime_file, qs_file, output_dir, threshold, consider_status):
    """运行完整的分析流程"""
    settings = ctx.obj['settings']
    start_time = time.time()
    
    # 设置输出目录
    if output_dir:
        settings.file_paths.output_dir = Path(output_dir)
        settings.ensure_directories()
    
    # 使用命令行参数或配置文件中的路径
    cime_path = cime_file or settings.file_paths.cime_file
    qs_path = qs_file or settings.file_paths.qs_file
    match_threshold = threshold or settings.processing.match_threshold
    
    if not cime_path or not qs_path:
        console.print("[red]错误: 请指定CIME和QS文件路径[/red]")
        return
    
    console.print("[bold blue]开始完整分析流程[/bold blue]")
    
    try:
        with Progress(console=console) as progress:
            # 并行解析CIME和QS
            parse_task = progress.add_task("解析文件...", total=2)
            
            with concurrent.futures.ThreadPoolExecutor() as executor:
                cime_future = executor.submit(
                    lambda: CIMEParser(cime_path).run(
                        str(settings.file_paths.get_full_path(settings.file_paths.cime_topology_file)),
                        str(settings.file_paths.get_full_path(settings.file_paths.single_node_edges_file))
                    )
                )
                qs_future = executor.submit(
                    lambda: QSStatusParser(qs_path).run(
                        str(settings.file_paths.get_full_path(settings.file_paths.qs_status_file))
                    )
                )
                
                cime_result = cime_future.result()
                progress.advance(parse_task)
                qs_result = qs_future.result()
                progress.advance(parse_task)
            
            # 拓扑匹配
            match_task = progress.add_task("匹配拓扑...", total=1)
            matcher = TopologyMatcher(
                str(settings.file_paths.get_full_path(settings.file_paths.cime_topology_file)),
                str(settings.file_paths.get_full_path(settings.file_paths.qs_status_file)),
                match_threshold
            )
            match_result = matcher.run(
                str(settings.file_paths.get_full_path(settings.file_paths.matched_output)),
                str(settings.file_paths.get_full_path(settings.file_paths.unmatched_output))
            )
            progress.advance(match_task)
            
            # 清洗拓扑
            clean_task = progress.add_task("清洗拓扑...", total=1)
            cleaner = TopologyCleaner(str(settings.file_paths.get_full_path(settings.file_paths.matched_output)))
            clean_result = cleaner.run(str(settings.file_paths.get_full_path(settings.file_paths.clean_output)))
            progress.advance(clean_task)
            
            # 岛屿分析
            island_task = progress.add_task("分析岛屿...", total=1)
            run_island_partition(
                str(settings.file_paths.get_full_path(settings.file_paths.clean_output)),
                str(settings.file_paths.get_full_path(settings.file_paths.island_output)),
                consider_status
            )
            progress.advance(island_task)
        
        # 显示最终结果
        elapsed_time = time.time() - start_time
        
        table = Table(title="完整分析结果")
        table.add_column("阶段", style="cyan")
        table.add_column("结果", style="green")
        
        table.add_row("CIME解析", f"{len(cime_result['nodes'])} 节点, {len(cime_result['edges'])} 边")
        table.add_row("QS解析", f"{len(qs_result['nodes'])} 节点状态, {len(qs_result['devices'])} 设备状态")
        table.add_row("拓扑匹配", f"节点匹配率: {match_result['node_match_rate']:.2%}, 设备匹配率: {match_result['device_match_rate']:.2%}")
        table.add_row("拓扑清洗", f"保留 {clean_result['clean_counts']['edges']} 条边")
        table.add_row("总耗时", f"{elapsed_time:.2f} 秒")
        table.add_row("输出目录", str(settings.file_paths.output_dir))
        
        console.print(table)
        console.print("[bold green]分析完成！[/bold green]")
        
    except Exception as e:
        console.print(f"[red]分析失败: {e}[/red]")


def main():
    """主入口函数"""
    cli()


if __name__ == "__main__":
    main()
