"""
拓扑匹配器

将QS状态信息匹配到CIME拓扑结构中，生成带状态信息的拓扑数据
"""

import json
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..config.logger_config import get_logger
from ..config.settings import settings
from ..utils.name_matcher import NameMatcher

logger = get_logger(__name__)


class TopologyMatchError(Exception):
    """拓扑匹配异常"""
    pass


class TopologyMatcher:
    """
    拓扑匹配器
    
    将QS状态文件中的状态信息匹配到CIME拓扑文件中的节点和设备，
    生成包含状态信息的完整拓扑数据
    """
    
    def __init__(
        self, 
        topo_path: str, 
        qs_status_path: str, 
        threshold: Optional[int] = None
    ):
        """
        初始化拓扑匹配器
        
        Args:
            topo_path: CIME拓扑文件路径
            qs_status_path: QS状态文件路径
            threshold: 名称匹配阈值，默认使用配置中的阈值
        """
        self.topo_path = Path(topo_path)
        self.qs_status_path = Path(qs_status_path)
        self.threshold = threshold or settings.processing.match_threshold
        
        # 验证文件存在
        if not self.topo_path.exists():
            raise FileNotFoundError(f"拓扑文件不存在: {topo_path}")
        if not self.qs_status_path.exists():
            raise FileNotFoundError(f"QS状态文件不存在: {qs_status_path}")
        
        # 初始化数据结构
        self.nodes: List[Dict[str, Any]] = []
        self.edges: List[Dict[str, Any]] = []
        self.node_status: Dict[str, str] = {}
        self.device_status: Dict[str, str] = {}
        
        self.unmatched_nodes: List[str] = []
        self.unmatched_devices: List[str] = []
        
        logger.info(f"初始化拓扑匹配器，阈值: {self.threshold}")
    
    def load_files(self) -> None:
        """加载QS状态和CIME拓扑文件"""
        try:
            # 加载QS状态
            with open(self.qs_status_path, "r", encoding="utf-8") as f:
                qs_status = json.load(f)
            self.node_status = qs_status.get("nodes", {})
            self.device_status = qs_status.get("devices", {})
            
            logger.info(
                f"加载QS状态: {len(self.node_status)} 个节点状态, "
                f"{len(self.device_status)} 个设备状态"
            )
            
            # 加载CIME拓扑
            with open(self.topo_path, "r", encoding="utf-8") as f:
                topo = json.load(f)
            self.nodes = topo.get("nodes", [])
            self.edges = topo.get("edges", [])
            
            logger.info(
                f"加载CIME拓扑: {len(self.nodes)} 个节点, "
                f"{len(self.edges)} 条边"
            )
            
        except Exception as e:
            raise TopologyMatchError(f"加载文件失败: {e}")
    
    def match_nodes(self) -> None:
        """匹配节点状态"""
        if not self.node_status:
            logger.warning("没有节点状态数据可匹配")
            return
        
        # 创建名称匹配器
        qs_node_names = list(self.node_status.keys())
        matcher = NameMatcher(qs_node_names, self.threshold)
        
        matched_count = 0
        for node in self.nodes:
            node_name = node.get("name", "")
            if not node_name:
                continue
            
            # 尝试匹配
            matched_name = matcher.match(node_name)
            if matched_name:
                node["status"] = self.node_status[matched_name]
                node["matched_name"] = matched_name
                matched_count += 1
            else:
                node["status"] = "UNKNOWN"
                self.unmatched_nodes.append(node_name)
        
        logger.info(
            f"节点匹配完成: {matched_count}/{len(self.nodes)} 个节点匹配成功, "
            f"{len(self.unmatched_nodes)} 个节点未匹配"
        )
    
    def match_devices(self) -> None:
        """匹配设备状态"""
        if not self.device_status:
            logger.warning("没有设备状态数据可匹配")
            return
        
        # 创建名称匹配器
        qs_device_names = list(self.device_status.keys())
        matcher = NameMatcher(qs_device_names, self.threshold)
        
        matched_count = 0
        total_devices = 0
        
        for edge in self.edges:
            devices = edge.get("devices", [])
            devices_status = []
            
            for device_name in devices:
                total_devices += 1
                
                # 尝试匹配设备状态
                matched_name = matcher.match(device_name)
                if matched_name:
                    status = self.device_status[matched_name]
                    matched_count += 1
                else:
                    status = "UNKNOWN"
                    self.unmatched_devices.append(device_name)
                
                devices_status.append({
                    "name": device_name,
                    "status": status,
                    "matched_name": matched_name
                })
            
            edge["devices_status"] = devices_status
        
        logger.info(
            f"设备匹配完成: {matched_count}/{total_devices} 个设备匹配成功, "
            f"{len(self.unmatched_devices)} 个设备未匹配"
        )
    
    def set_transformer_edges_closed(self) -> None:
        """将所有变压器设备状态设为CLOSED"""
        transformer_count = 0
        
        for edge in self.edges:
            devices_status = edge.get("devices_status", [])
            for dev_status in devices_status:
                if dev_status["name"].startswith("Transformer-"):
                    dev_status["status"] = "CLOSED"
                    transformer_count += 1
        
        logger.info(f"设置变压器状态为CLOSED: {transformer_count} 个变压器设备")
    
    def save_matched_topology(self, output_path: str) -> None:
        """保存匹配后的拓扑"""
        matched_topo = {
            "nodes": self.nodes,
            "edges": self.edges,
            "metadata": {
                "total_nodes": len(self.nodes),
                "total_edges": len(self.edges),
                "matched_nodes": len(self.nodes) - len(self.unmatched_nodes),
                "matched_devices": sum(
                    len([d for d in edge.get("devices_status", []) 
                         if d.get("status") != "UNKNOWN"])
                    for edge in self.edges
                ),
                "match_threshold": self.threshold
            }
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(matched_topo, f, ensure_ascii=False, indent=2)
        
        logger.info(f"匹配后的拓扑已保存到: {output_file}")
    
    def save_unmatched_elements(self, output_path: str) -> None:
        """保存未匹配的元素"""
        unmatched_data = {
            "unmatched_nodes": self.unmatched_nodes,
            "unmatched_devices": self.unmatched_devices,
            "statistics": {
                "unmatched_node_count": len(self.unmatched_nodes),
                "unmatched_device_count": len(self.unmatched_devices),
                "total_node_count": len(self.nodes),
                "total_device_count": sum(
                    len(edge.get("devices", [])) for edge in self.edges
                )
            }
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(unmatched_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"未匹配元素已保存到: {output_file}")
    
    def run(self, matched_topo_path: str, unmatched_path: str) -> Dict[str, Any]:
        """
        执行完整匹配流程
        
        Args:
            matched_topo_path: 匹配后拓扑输出路径
            unmatched_path: 未匹配元素输出路径
            
        Returns:
            匹配结果统计
        """
        logger.info("开始拓扑匹配流程")
        
        try:
            # 执行匹配步骤
            self.load_files()
            self.match_nodes()
            self.match_devices()
            self.set_transformer_edges_closed()
            
            # 保存结果
            self.save_matched_topology(matched_topo_path)
            self.save_unmatched_elements(unmatched_path)
            
            # 统计结果
            total_devices = sum(len(edge.get("devices", [])) for edge in self.edges)
            matched_devices = total_devices - len(self.unmatched_devices)
            
            result = {
                "total_nodes": len(self.nodes),
                "matched_nodes": len(self.nodes) - len(self.unmatched_nodes),
                "total_devices": total_devices,
                "matched_devices": matched_devices,
                "node_match_rate": (len(self.nodes) - len(self.unmatched_nodes)) / len(self.nodes) if self.nodes else 0,
                "device_match_rate": matched_devices / total_devices if total_devices else 0
            }
            
            logger.info(
                f"拓扑匹配完成 - 节点匹配率: {result['node_match_rate']:.2%}, "
                f"设备匹配率: {result['device_match_rate']:.2%}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"拓扑匹配失败: {e}")
            raise TopologyMatchError(f"拓扑匹配失败: {e}")
