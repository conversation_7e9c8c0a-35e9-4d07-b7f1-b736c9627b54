"""
拓扑清洗器

清洗拓扑数据，移除状态未知的节点和边，确保数据质量
"""

import json
from pathlib import Path
from typing import List, Dict, Any

from ..config.logger_config import get_logger

logger = get_logger(__name__)


class TopologyCleanError(Exception):
    """拓扑清洗异常"""
    pass


class TopologyCleaner:
    """
    拓扑清洗器
    
    清洗拓扑数据，移除状态未知的节点和边，
    确保后续分析使用的数据质量
    """
    
    def __init__(self, topo_path: str):
        """
        初始化拓扑清洗器
        
        Args:
            topo_path: 待清洗的拓扑文件路径
        """
        self.topo_path = Path(topo_path)
        
        # 验证文件存在
        if not self.topo_path.exists():
            raise FileNotFoundError(f"拓扑文件不存在: {topo_path}")
        
        # 初始化数据结构
        self.nodes: List[Dict[str, Any]] = []
        self.edges: List[Dict[str, Any]] = []
        self.clean_nodes: List[Dict[str, Any]] = []
        self.clean_edges: List[Dict[str, Any]] = []
        
        logger.info(f"初始化拓扑清洗器，文件: {self.topo_path}")
    
    def load_topology(self) -> None:
        """加载拓扑JSON文件"""
        try:
            with open(self.topo_path, "r", encoding="utf-8") as f:
                topo = json.load(f)
            
            self.nodes = topo.get("nodes", [])
            self.edges = topo.get("edges", [])
            
            logger.info(
                f"加载拓扑数据: {len(self.nodes)} 个节点, "
                f"{len(self.edges)} 条边"
            )
            
        except Exception as e:
            raise TopologyCleanError(f"加载拓扑文件失败: {e}")
    
    def clean_nodes_edges(self) -> None:
        """清洗节点和边，移除状态未知的元素"""
        # 清洗节点 - 目前保留所有节点
        # 可以根据需要添加节点过滤逻辑
        self.clean_nodes = [node for node in self.nodes]
        
        # 获取清洗后的节点ID集合
        clean_node_ids = {
            node["id"] for node in self.clean_nodes 
            if "id" in node
        }
        
        # 清洗边 - 移除包含状态未知设备的边
        clean_edges = []
        removed_edges = 0
        
        for edge in self.edges:
            # 检查边的端点是否在清洗后的节点中
            from_raw = edge.get("from", "")
            to_raw = edge.get("to", "")
            
            from_id = from_raw.split(" ")[0] if from_raw else ""
            to_id = to_raw.split(" ")[0] if to_raw else ""
            
            # 如果端点节点不存在，跳过这条边
            if from_id not in clean_node_ids or to_id not in clean_node_ids:
                removed_edges += 1
                logger.debug(f"移除边（端点节点不存在）: {from_raw} -> {to_raw}")
                continue
            
            # 检查设备状态
            devices_status = edge.get("devices_status", [])
            has_unknown_device = any(
                d.get("status") == "UNKNOWN" for d in devices_status
            )
            
            if has_unknown_device:
                removed_edges += 1
                unknown_devices = [
                    d.get("name", "未知设备") 
                    for d in devices_status 
                    if d.get("status") == "UNKNOWN"
                ]
                logger.debug(
                    f"移除边（包含未知状态设备）: {from_raw} -> {to_raw}, "
                    f"未知设备: {unknown_devices}"
                )
                continue
            
            clean_edges.append(edge)
        
        self.clean_edges = clean_edges
        
        logger.info(
            f"边清洗完成: 保留 {len(self.clean_edges)} 条边, "
            f"移除 {removed_edges} 条边"
        )
    
    def validate_topology(self) -> Dict[str, Any]:
        """验证清洗后的拓扑数据"""
        # 统计节点类型
        node_types = {}
        for node in self.clean_nodes:
            node_type = node.get("type", "Unknown")
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        # 统计边的设备状态
        device_status_stats = {"CLOSED": 0, "OPEN": 0, "UNKNOWN": 0}
        total_devices = 0
        
        for edge in self.clean_edges:
            devices_status = edge.get("devices_status", [])
            for device in devices_status:
                status = device.get("status", "UNKNOWN")
                device_status_stats[status] = device_status_stats.get(status, 0) + 1
                total_devices += 1
        
        # 检查连通性 - 统计孤立节点
        connected_nodes = set()
        for edge in self.clean_edges:
            from_id = edge.get("from", "").split(" ")[0]
            to_id = edge.get("to", "").split(" ")[0]
            connected_nodes.add(from_id)
            connected_nodes.add(to_id)
        
        all_node_ids = {node.get("id") for node in self.clean_nodes if "id" in node}
        isolated_nodes = all_node_ids - connected_nodes
        
        validation_result = {
            "total_nodes": len(self.clean_nodes),
            "total_edges": len(self.clean_edges),
            "node_types": node_types,
            "total_devices": total_devices,
            "device_status_distribution": device_status_stats,
            "connected_nodes": len(connected_nodes),
            "isolated_nodes": len(isolated_nodes),
            "connectivity_rate": len(connected_nodes) / len(all_node_ids) if all_node_ids else 0
        }
        
        logger.info(
            f"拓扑验证结果: {validation_result['total_nodes']} 个节点, "
            f"{validation_result['total_edges']} 条边, "
            f"连通率: {validation_result['connectivity_rate']:.2%}"
        )
        
        return validation_result
    
    def save_clean_topology(self, output_path: str) -> None:
        """保存清洗后的拓扑"""
        clean_topo = {
            "nodes": self.clean_nodes,
            "edges": self.clean_edges,
            "metadata": {
                "original_node_count": len(self.nodes),
                "original_edge_count": len(self.edges),
                "clean_node_count": len(self.clean_nodes),
                "clean_edge_count": len(self.clean_edges),
                "removed_edge_count": len(self.edges) - len(self.clean_edges)
            }
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(clean_topo, f, ensure_ascii=False, indent=2)
        
        logger.info(f"清洗后的拓扑已保存到: {output_file}")
    
    def run(self, output_path: str) -> Dict[str, Any]:
        """
        执行完整清洗流程
        
        Args:
            output_path: 清洗后拓扑输出路径
            
        Returns:
            清洗结果统计
        """
        logger.info("开始拓扑清洗流程")
        
        try:
            # 执行清洗步骤
            self.load_topology()
            self.clean_nodes_edges()
            validation_result = self.validate_topology()
            self.save_clean_topology(output_path)
            
            # 生成清洗结果统计
            result = {
                "original_counts": {
                    "nodes": len(self.nodes),
                    "edges": len(self.edges)
                },
                "clean_counts": {
                    "nodes": len(self.clean_nodes),
                    "edges": len(self.clean_edges)
                },
                "removed_counts": {
                    "nodes": len(self.nodes) - len(self.clean_nodes),
                    "edges": len(self.edges) - len(self.clean_edges)
                },
                "validation": validation_result
            }
            
            logger.info(
                f"拓扑清洗完成 - 保留节点: {result['clean_counts']['nodes']}, "
                f"保留边: {result['clean_counts']['edges']}, "
                f"移除边: {result['removed_counts']['edges']}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"拓扑清洗失败: {e}")
            raise TopologyCleanError(f"拓扑清洗失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取清洗统计信息"""
        if not self.clean_nodes or not self.clean_edges:
            return {"error": "尚未执行清洗操作"}
        
        return {
            "node_retention_rate": len(self.clean_nodes) / len(self.nodes) if self.nodes else 0,
            "edge_retention_rate": len(self.clean_edges) / len(self.edges) if self.edges else 0,
            "total_devices": sum(
                len(edge.get("devices_status", [])) 
                for edge in self.clean_edges
            ),
            "clean_device_count": sum(
                len([d for d in edge.get("devices_status", []) 
                     if d.get("status") != "UNKNOWN"])
                for edge in self.clean_edges
            )
        }
