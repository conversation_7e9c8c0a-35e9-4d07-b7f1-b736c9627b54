"""
QS状态文件解析器

用于解析QS格式的电力系统状态文件，提取节点和设备的状态信息
"""

import json
import re
from pathlib import Path
from typing import Dict, Any, Optional

from ..config.logger_config import get_logger
from ..config.settings import settings

logger = get_logger(__name__)


class QSParseError(Exception):
    """QS解析异常"""
    pass


class QSStatusParser:
    """
    QS状态文件解析器
    
    解析QS格式的电力系统状态文件，提取节点和设备的开关状态信息
    """
    
    def __init__(self, qs_file: str, encoding: Optional[str] = None):
        """
        初始化QS状态解析器
        
        Args:
            qs_file: QS文件路径
            encoding: 文件编码，默认使用配置中的编码
        """
        self.qs_file = Path(qs_file)
        self.encoding = encoding or settings.processing.qs_encoding
        
        # 验证文件存在
        if not self.qs_file.exists():
            raise FileNotFoundError(f"QS文件不存在: {qs_file}")
        
        # 初始化数据结构
        self.blocks: Dict[str, Dict[str, Any]] = {}
        self.node_status: Dict[str, str] = {}
        self.device_status: Dict[str, str] = {}
        self.lines: list = []
        
        logger.info(f"初始化QS状态解析器，文件: {self.qs_file}")
    
    def parse_file(self) -> None:
        """读取QS文件并按行去除空行"""
        try:
            with open(self.qs_file, "r", encoding=self.encoding) as f:
                self.lines = [line.rstrip() for line in f if line.strip()]
            
            logger.info(f"读取QS文件，共 {len(self.lines)} 行")
            
        except Exception as e:
            raise QSParseError(f"读取QS文件失败: {e}")
    
    def parse_blocks(self) -> None:
        """解析标签块，生成 {tag: {header, data}}"""
        current_tag = None
        header = []
        
        logger.info("开始解析QS文件标签块")
        
        for line_num, line in enumerate(self.lines, 1):
            try:
                # 匹配标签开始
                m_tag_start = re.match(r"<([^>]+)>", line)
                if m_tag_start:
                    tag_full = m_tag_start.group(1)
                    tag = tag_full.split("::")[0].strip()
                    current_tag = tag
                    self.blocks[current_tag] = {"header": [], "data": []}
                    continue

                # 匹配标签结束
                m_tag_end = re.match(r"</([^>]+)>", line)
                if m_tag_end:
                    tag_full = m_tag_end.group(1)
                    tag = tag_full.split("::")[0].strip()
                    if current_tag == tag:
                        current_tag = None
                    continue

                # 处理标签内容
                if current_tag:
                    if line.startswith("@"):
                        # 解析表头
                        header = re.split(r"\s+", line[1:].strip())
                        self.blocks[current_tag]["header"] = header
                    elif line.startswith("#"):
                        # 解析数据行
                        values = re.split(r"\s+", line[1:].strip())
                        if len(values) < len(header):
                            values += [""] * (len(header) - len(values))
                        row = dict(zip(header, values))
                        if "id" in row:
                            row["id"] = row["id"].lstrip("#")
                        self.blocks[current_tag]["data"].append(row)
            
            except Exception as e:
                logger.warning(f"解析第 {line_num} 行时出错: {e}")
                continue
        
        logger.info(f"标签块解析完成，共 {len(self.blocks)} 个标签块")
    
    def extract_node_status(self) -> None:
        """提取节点状态，CLOSED/OPEN"""
        node_count = 0
        
        # 解析Bus节点状态
        bus_data = self.blocks.get("Bus", {}).get("data", [])
        for bus in bus_data:
            name = bus.get("name")
            if not name:
                continue
            
            status = bus.get("off", "0")
            self.node_status[name] = "CLOSED" if status in ["0"] else "OPEN"
            node_count += 1
        
        # 解析Load节点状态
        load_data = self.blocks.get("Load", {}).get("data", [])
        for load in load_data:
            name = load.get("name")
            if not name:
                continue
            
            status = load.get("off", "1")
            self.node_status[name] = "CLOSED" if status in ["0"] else "OPEN"
            node_count += 1
        
        # 解析Transformer每个绕组端状态
        transformer_data = self.blocks.get("Transformer", {}).get("data", [])
        for tf in transformer_data:
            tf_name = tf.get("name", "")
            if not tf_name:
                continue
            
            for side, side_name in zip(["I", "K", "J"], ["高", "中", "低"]):
                name = f"{tf_name}-{side_name}"
                off = tf.get(f"{side}_off", "1")
                self.node_status[name] = "CLOSED" if off in ["0"] else "OPEN"
                node_count += 1
        
        logger.info(f"节点状态提取完成，共 {node_count} 个节点")
    
    def extract_device_status(self) -> None:
        """提取设备状态"""
        device_count = 0
        
        # 解析断路器和隔离开关状态
        for tag in ["Breaker", "Disconnector"]:
            device_data = self.blocks.get(tag, {}).get("data", [])
            for dev in device_data:
                name = dev.get("name")
                if not name:
                    continue
                
                state = dev.get("point", "0")
                self.device_status[name] = "CLOSED" if state in ["1"] else "OPEN"
                device_count += 1
        
        # 解析ACLine状态
        acline_data = self.blocks.get("ACline", {}).get("data", [])
        for line in acline_data:
            name = line.get("name")
            if not name:
                continue
            
            i_off = line.get("I_off", "0")
            j_off = line.get("J_off", "0")
            # 只有两端都投入时才认为线路是闭合的
            self.device_status[name] = "CLOSED" if i_off in ["0"] and j_off in ["0"] else "OPEN"
            device_count += 1
        
        logger.info(f"设备状态提取完成，共 {device_count} 个设备")
    
    def to_dict(self) -> Dict[str, Dict[str, str]]:
        """转换为字典格式"""
        return {
            "nodes": self.node_status,
            "devices": self.device_status
        }
    
    def save_json(self, path: str) -> None:
        """保存为JSON文件"""
        output_path = Path(path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
        
        logger.info(f"QS状态数据已保存到: {output_path}")
    
    def run(self, output_path: Optional[str] = None) -> Dict[str, Dict[str, str]]:
        """
        执行完整的解析流程
        
        Args:
            output_path: 输出文件路径，如果为None则不保存文件
            
        Returns:
            状态字典
        """
        logger.info("开始QS状态文件解析流程")
        
        try:
            # 执行解析步骤
            self.parse_file()
            self.parse_blocks()
            self.extract_node_status()
            self.extract_device_status()
            
            # 保存结果（如果指定了输出路径）
            if output_path:
                self.save_json(output_path)
            
            result = self.to_dict()
            logger.info(
                f"QS状态解析完成，节点状态: {len(result['nodes'])} 个，"
                f"设备状态: {len(result['devices'])} 个"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"QS状态解析失败: {e}")
            raise QSParseError(f"QS状态解析失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取状态统计信息"""
        node_stats = {"CLOSED": 0, "OPEN": 0}
        device_stats = {"CLOSED": 0, "OPEN": 0}
        
        for status in self.node_status.values():
            node_stats[status] = node_stats.get(status, 0) + 1
        
        for status in self.device_status.values():
            device_stats[status] = device_stats.get(status, 0) + 1
        
        return {
            "total_nodes": len(self.node_status),
            "total_devices": len(self.device_status),
            "node_status_distribution": node_stats,
            "device_status_distribution": device_stats
        }
