"""
名称匹配工具模块

提供电力系统设备和节点名称的标准化和模糊匹配功能
"""

from typing import List, Optional, Dict, Any
from rapidfuzz import process, fuzz


# 罗马数字映射
ROMAN_MAP = {
    "Ⅰ": "I", "Ⅱ": "II", "Ⅲ": "III", "Ⅳ": "IV", "Ⅴ": "V", "Ⅵ": "VI"
}

# 同义词映射
SYNONYMS = {
    "旁路": "旁母",
    # 可以继续添加同义词
}


def normalize_name(name: str) -> str:
    """
    标准化名称：同义词替换、罗马数字转换、去空格、大写
    
    Args:
        name: 原始名称
        
    Returns:
        标准化后的名称
    """
    if not name:
        return ""
    
    # 转大写并去除空格
    name = name.upper().replace(" ", "")
    
    # 罗马数字转换
    for roman, arabic in ROMAN_MAP.items():
        name = name.replace(roman, arabic)
    
    # 同义词替换
    for synonym, standard in SYNONYMS.items():
        name = name.replace(synonym.upper(), standard.upper())
    
    return name


class NameMatcher:
    """
    名称匹配器
    
    支持精确匹配和模糊匹配，用于电力系统设备和节点名称的匹配
    """
    
    def __init__(self, reference_names: List[str], threshold: int = 80):
        """
        初始化名称匹配器
        
        Args:
            reference_names: 参考名称列表
            threshold: 模糊匹配阈值 (0-100)
        """
        if not reference_names:
            raise ValueError("参考名称列表不能为空")
        
        if not 0 <= threshold <= 100:
            raise ValueError("阈值必须在0-100之间")
        
        self.threshold = threshold
        self.ref_names_raw = reference_names
        self.ref_names_set = set(reference_names)  # 快速精准匹配
        self.ref_names_norm = [normalize_name(name) for name in reference_names]
    
    def match(self, name: str) -> Optional[str]:
        """
        匹配名称
        
        Args:
            name: 待匹配的名称
            
        Returns:
            匹配到的参考名称，如果没有匹配则返回None
        """
        if not name:
            return None

        # 1. 精准匹配原始名称
        if name in self.ref_names_set:
            return name

        # 2. 标准化后精准匹配
        norm_name = normalize_name(name)
        for ref_norm, ref_raw in zip(self.ref_names_norm, self.ref_names_raw):
            if norm_name == ref_norm:
                return ref_raw

        # 3. 标准化后模糊匹配
        match_result = process.extractOne(
            norm_name, 
            self.ref_names_norm, 
            scorer=fuzz.ratio
        )
        
        if match_result:
            ref_match_norm, score, idx = match_result
            if score >= self.threshold:
                return self.ref_names_raw[idx]

        # 无匹配
        return None
    
    def match_with_score(self, name: str) -> Optional[Dict[str, Any]]:
        """
        匹配名称并返回匹配分数
        
        Args:
            name: 待匹配的名称
            
        Returns:
            包含匹配结果和分数的字典，如果没有匹配则返回None
        """
        if not name:
            return None

        # 1. 精准匹配原始名称
        if name in self.ref_names_set:
            return {
                "matched_name": name,
                "score": 100,
                "match_type": "exact_original"
            }

        # 2. 标准化后精准匹配
        norm_name = normalize_name(name)
        for ref_norm, ref_raw in zip(self.ref_names_norm, self.ref_names_raw):
            if norm_name == ref_norm:
                return {
                    "matched_name": ref_raw,
                    "score": 100,
                    "match_type": "exact_normalized"
                }

        # 3. 标准化后模糊匹配
        match_result = process.extractOne(
            norm_name, 
            self.ref_names_norm, 
            scorer=fuzz.ratio
        )
        
        if match_result:
            ref_match_norm, score, idx = match_result
            if score >= self.threshold:
                return {
                    "matched_name": self.ref_names_raw[idx],
                    "score": score,
                    "match_type": "fuzzy"
                }

        # 无匹配
        return None
    
    def batch_match(self, names: List[str]) -> Dict[str, Optional[str]]:
        """
        批量匹配名称
        
        Args:
            names: 待匹配的名称列表
            
        Returns:
            名称匹配结果字典
        """
        return {name: self.match(name) for name in names}
