"""
配置管理模块

支持从环境变量、配置文件等多种方式加载配置
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
from pydantic import BaseSettings, Field, validator


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    username: str = Field(default="postgres", env="DB_USERNAME")
    password: str = Field(default="", env="DB_PASSWORD")
    database: str = Field(default="topo_analyzer", env="DB_DATABASE")


class FilePathConfig(BaseSettings):
    """文件路径配置"""
    data_dir: Path = Field(default=Path("./data"), env="DATA_DIR")
    output_dir: Path = Field(default=Path("./out"), env="OUTPUT_DIR")
    
    # 输入文件
    cime_file: Optional[str] = Field(default=None, env="CIME_FILE")
    qs_file: Optional[str] = Field(default=None, env="QS_FILE")
    
    # 输出文件
    cime_topology_file: str = Field(default="cime_topology.json", env="CIME_TOPOLOGY_FILE")
    single_node_edges_file: str = Field(default="single_node_edges.json", env="SINGLE_NODE_EDGES_FILE")
    qs_status_file: str = Field(default="qs_status.json", env="QS_STATUS_FILE")
    matched_output: str = Field(default="combined_topology_with_match.json", env="MATCHED_OUTPUT")
    unmatched_output: str = Field(default="unmatched_elements.json", env="UNMATCHED_OUTPUT")
    clean_output: str = Field(default="clean_topology.json", env="CLEAN_OUTPUT")
    island_output: str = Field(default="cime_islands.json", env="ISLAND_OUTPUT")
    
    def get_full_path(self, filename: str) -> Path:
        """获取输出文件的完整路径"""
        return self.output_dir / filename


class ProcessingConfig(BaseSettings):
    """处理配置"""
    # 匹配阈值
    match_threshold: int = Field(default=80, env="MATCH_THRESHOLD")

    # 编码设置
    cime_encoding: str = Field(default="gbk", env="CIME_ENCODING")
    qs_encoding: str = Field(default="gbk", env="QS_ENCODING")

    # 并发设置
    max_workers: int = Field(default=4, env="MAX_WORKERS")

    # 岛屿分析设置
    consider_status: bool = Field(default=False, env="CONSIDER_STATUS")

    @validator('match_threshold')
    def validate_match_threshold(cls, v):
        if not 0 <= v <= 100:
            raise ValueError('匹配阈值必须在0-100之间')
        return v

    @validator('max_workers')
    def validate_max_workers(cls, v):
        if v < 1:
            raise ValueError('最大工作线程数必须大于0')
        return v


class LoggingConfig(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s [%(levelname)s] [%(name)s] %(message)s",
        env="LOG_FORMAT"
    )
    date_format: str = Field(default="%Y-%m-%d %H:%M:%S", env="LOG_DATE_FORMAT")
    file_path: Optional[str] = Field(default=None, env="LOG_FILE_PATH")
    max_file_size: int = Field(default=10485760, env="LOG_MAX_FILE_SIZE")  # 10MB
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")


class Settings(BaseSettings):
    """主配置类"""
    
    # 环境设置
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    
    # 子配置
    database: DatabaseConfig = DatabaseConfig()
    file_paths: FilePathConfig = FilePathConfig()
    processing: ProcessingConfig = ProcessingConfig()
    logging: LoggingConfig = LoggingConfig()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @classmethod
    def from_yaml(cls, config_path: str) -> "Settings":
        """从YAML文件加载配置"""
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_file, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.dict()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        self.file_paths.data_dir.mkdir(parents=True, exist_ok=True)
        self.file_paths.output_dir.mkdir(parents=True, exist_ok=True)


# 全局配置实例
settings = Settings()
