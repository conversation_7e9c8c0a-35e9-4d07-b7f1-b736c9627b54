"""
岛屿分析器

分析电力系统拓扑的连通性，识别电气岛屿
"""

import json
from collections import defaultdict, deque
from pathlib import Path
from typing import Dict, List, Any, Set, Optional, Union

from ..config.logger_config import get_logger

logger = get_logger(__name__)


class IslandAnalysisError(Exception):
    """岛屿分析异常"""
    pass


class IslandAnalyzer:
    """
    岛屿分析器
    
    分析电力系统拓扑的连通性，识别电气岛屿，
    支持考虑设备状态的连通性分析
    """
    
    def __init__(
        self, 
        nodes: Union[List[Dict], Dict[str, Dict]], 
        edges: List[Dict], 
        consider_status: bool = True
    ):
        """
        初始化岛屿分析器
        
        Args:
            nodes: 节点数据，可以是列表或字典
            edges: 边数据列表
            consider_status: 是否考虑设备状态
        """
        # 处理节点数据格式
        if isinstance(nodes, list):
            self.nodes = {n["id"]: n for n in nodes}
        elif isinstance(nodes, dict):
            self.nodes = nodes
        else:
            raise ValueError("nodes必须是列表或字典格式")
        
        self.edges = edges
        self.consider_status = consider_status
        
        # 初始化数据结构
        self.graph: Dict[str, List[str]] = defaultdict(list)
        self.islands: List[Dict[str, Any]] = []
        self.disconnected_edges: List[Dict[str, Any]] = []
        self.connected_edges: List[Dict[str, Any]] = []
        
        logger.info(
            f"初始化岛屿分析器: {len(self.nodes)} 个节点, "
            f"{len(self.edges)} 条边, 考虑状态: {consider_status}"
        )
        
        self.build_graph()
    
    def build_graph(self) -> None:
        """构建邻接表，根据设备状态决定连通性"""
        self.disconnected_edges = []
        self.connected_edges = []
        total_edges = len(self.edges)
        
        logger.info(f"开始构建图，总边数: {total_edges}")
        
        for i, edge in enumerate(self.edges):
            try:
                # 提取节点ID
                from_node = edge["from"].split(" ")[0]
                to_node = edge["to"].split(" ")[0]
                
                # 检查节点是否存在
                if from_node not in self.nodes or to_node not in self.nodes:
                    logger.warning(f"边 {i+1}/{total_edges}: 节点不存在 - {from_node} 或 {to_node}")
                    continue
                
                # 根据是否考虑状态决定连通性
                if self.consider_status:
                    devices_status = edge.get("devices_status", [])
                    open_devices = [
                        dev["name"] for dev in devices_status 
                        if dev.get("status") == "OPEN"
                    ]
                    closed_devices = [
                        dev["name"] for dev in devices_status 
                        if dev.get("status") == "CLOSED"
                    ]
                    
                    if open_devices:
                        # 记录断开的边
                        edge_info = {
                            "from": from_node,
                            "to": to_node,
                            "from_name": self.nodes[from_node].get("name", "未知"),
                            "to_name": self.nodes[to_node].get("name", "未知"),
                            "open_devices": open_devices,
                            "closed_devices": closed_devices,
                            "edge_index": i
                        }
                        self.disconnected_edges.append(edge_info)
                        logger.debug(
                            f"断开边: {self.nodes[from_node].get('name')} -> "
                            f"{self.nodes[to_node].get('name')}, 断开设备: {open_devices}"
                        )
                        continue
                    else:
                        # 记录连通的边
                        self.connected_edges.append({
                            "from": from_node,
                            "to": to_node,
                            "from_name": self.nodes[from_node].get("name", "未知"),
                            "to_name": self.nodes[to_node].get("name", "未知"),
                            "devices": closed_devices,
                            "edge_index": i
                        })
                
                # 添加到邻接表（如果不考虑状态或所有设备都是闭合的）
                if not self.consider_status or not any(
                    dev.get("status") == "OPEN" 
                    for dev in edge.get("devices_status", [])
                ):
                    self.graph[from_node].append(to_node)
                    self.graph[to_node].append(from_node)
            
            except Exception as e:
                logger.warning(f"处理边 {i+1} 时出错: {e}")
                continue
        
        logger.info(
            f"图构建完成: {len(self.connected_edges)} 条连通边, "
            f"{len(self.disconnected_edges)} 条断开边"
        )
    
    def find_islands(self) -> None:
        """使用DFS查找所有连通分量（岛屿）"""
        visited: Set[str] = set()
        self.islands = []
        
        logger.info("开始查找岛屿")
        
        for node_id in self.nodes:
            if node_id not in visited:
                # 发现新岛屿
                island_nodes = []
                stack = [node_id]
                
                while stack:
                    current = stack.pop()
                    if current not in visited:
                        visited.add(current)
                        island_nodes.append(current)
                        
                        # 添加所有邻接节点
                        for neighbor in self.graph.get(current, []):
                            if neighbor not in visited:
                                stack.append(neighbor)
                
                # 创建岛屿信息
                island_info = self._create_island_info(island_nodes, len(self.islands) + 1)
                self.islands.append(island_info)
        
        # 按节点数量排序
        self.islands.sort(key=lambda x: x["node_count"], reverse=True)
        
        logger.info(f"岛屿查找完成，共发现 {len(self.islands)} 个岛屿")
    
    def _create_island_info(self, node_ids: List[str], island_id: int) -> Dict[str, Any]:
        """创建岛屿信息"""
        # 收集节点详细信息
        nodes_detail = []
        voltage_levels = set()
        substations = set()
        node_types = defaultdict(int)
        
        for node_id in node_ids:
            node = self.nodes.get(node_id, {})
            nodes_detail.append({
                "id": node_id,
                "name": node.get("name", "未知节点"),
                "type": node.get("type", "Unknown"),
                "voltage": node.get("voltage", "未知"),
                "substation": node.get("substation", "未知厂站")
            })
            
            # 统计信息
            if node.get("voltage"):
                voltage_levels.add(node["voltage"])
            if node.get("substation"):
                substations.add(node["substation"])
            node_types[node.get("type", "Unknown")] += 1
        
        # 查找岛屿内的边
        island_edges = []
        for edge in self.connected_edges:
            if edge["from"] in node_ids and edge["to"] in node_ids:
                island_edges.append(edge)
        
        return {
            "island_id": island_id,
            "node_count": len(node_ids),
            "edge_count": len(island_edges),
            "nodes": nodes_detail,
            "edges": island_edges,
            "voltage_levels": sorted(list(voltage_levels)),
            "substations": sorted(list(substations)),
            "node_types": dict(node_types),
            "is_main_island": len(node_ids) == max(len(island["nodes"]) for island in self.islands) if self.islands else True
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取岛屿分析统计信息"""
        if not self.islands:
            return {"error": "尚未执行岛屿分析"}
        
        total_nodes = sum(island["node_count"] for island in self.islands)
        main_island = max(self.islands, key=lambda x: x["node_count"])
        
        return {
            "total_islands": len(self.islands),
            "total_nodes": total_nodes,
            "total_connected_edges": len(self.connected_edges),
            "total_disconnected_edges": len(self.disconnected_edges),
            "main_island_size": main_island["node_count"],
            "main_island_ratio": main_island["node_count"] / total_nodes if total_nodes > 0 else 0,
            "small_islands": len([i for i in self.islands if i["node_count"] == 1]),
            "connectivity_rate": len(self.connected_edges) / len(self.edges) if self.edges else 0
        }
    
    def save_islands_to_file(self, output_path: str) -> None:
        """保存岛屿分析结果到文件"""
        result = {
            "islands": self.islands,
            "statistics": self.get_statistics(),
            "disconnected_edges": self.disconnected_edges,
            "analysis_parameters": {
                "consider_status": self.consider_status,
                "total_nodes": len(self.nodes),
                "total_edges": len(self.edges)
            }
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"岛屿分析结果已保存到: {output_file}")
    
    def run_analysis(self, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        执行完整的岛屿分析
        
        Args:
            output_path: 输出文件路径，如果为None则不保存文件
            
        Returns:
            分析结果
        """
        logger.info("开始岛屿分析")
        
        try:
            self.find_islands()
            
            if output_path:
                self.save_islands_to_file(output_path)
            
            statistics = self.get_statistics()
            logger.info(
                f"岛屿分析完成: {statistics['total_islands']} 个岛屿, "
                f"主岛大小: {statistics['main_island_size']} 个节点"
            )
            
            return {
                "islands": self.islands,
                "statistics": statistics,
                "disconnected_edges": self.disconnected_edges
            }
            
        except Exception as e:
            logger.error(f"岛屿分析失败: {e}")
            raise IslandAnalysisError(f"岛屿分析失败: {e}")


def run_island_partition(
    topology_path: str, 
    output_path: str, 
    consider_status: bool = False
) -> bool:
    """
    运行岛屿分析的便捷函数
    
    Args:
        topology_path: 拓扑文件路径
        output_path: 输出文件路径
        consider_status: 是否考虑设备状态
        
    Returns:
        是否成功
    """
    try:
        # 加载拓扑数据
        with open(topology_path, "r", encoding="utf-8") as f:
            topo_data = json.load(f)
        
        nodes = topo_data.get("nodes", [])
        edges = topo_data.get("edges", [])
        
        # 创建分析器并运行分析
        analyzer = IslandAnalyzer(nodes, edges, consider_status)
        analyzer.run_analysis(output_path)
        
        return True
        
    except Exception as e:
        logger.error(f"岛屿分析失败: {e}")
        return False
