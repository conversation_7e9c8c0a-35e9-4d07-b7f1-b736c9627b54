# 电力系统拓扑分析器 (Topo Analyzer)

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/tests-passing-brightgreen.svg)](tests/)

一个用于解析CIME文件、QS状态文件，进行拓扑匹配、清洗和岛屿分析的Python包。

## 功能特性

- 🔍 **CIME文件解析**: 解析CIME格式的电力系统拓扑文件
- 📊 **QS状态解析**: 解析QS格式的设备状态文件
- 🔗 **拓扑匹配**: 智能匹配拓扑结构与设备状态
- 🧹 **数据清洗**: 清理无效数据，确保分析质量
- 🏝️ **岛屿分析**: 分析电力系统的连通性和电气岛屿
- 🛠️ **命令行工具**: 提供便捷的CLI接口
- ⚙️ **配置管理**: 灵活的配置系统支持多环境
- 🧪 **完整测试**: 全面的单元测试和集成测试

## 安装

### 从源码安装

```bash
git clone https://github.com/your-org/topo-analyzer.git
cd topo-analyzer
pip install -e .
```

### 开发环境安装

```bash
git clone https://github.com/your-org/topo-analyzer.git
cd topo-analyzer
pip install -e ".[dev]"
```

## 快速开始

### 命令行使用

```bash
# 运行完整分析流程
topo-analyzer run-all --cime-file data/sample.cime --qs-file data/sample.qs

# 单独解析CIME文件
topo-analyzer parse-cime --cime-file data/sample.cime --output out/topology.json

# 单独解析QS文件
topo-analyzer parse-qs --qs-file data/sample.qs --output out/status.json

# 匹配拓扑和状态
topo-analyzer match-topology --topo-file out/topology.json --status-file out/status.json

# 清洗拓扑数据
topo-analyzer clean-topology --input out/matched.json --output out/clean.json

# 分析岛屿
topo-analyzer analyze-islands --input out/clean.json --output out/islands.json
```

### Python API使用

```python
from topo_analyzer import TopologyAnalyzer, Settings

# 使用默认配置
analyzer = TopologyAnalyzer()

# 运行完整分析
result = analyzer.run_complete_analysis(
    cime_file="data/sample.cime",
    qs_file="data/sample.qs"
)

if result['success']:
    print("分析完成！")
    print(f"节点数: {result['cime_analysis']['nodes']}")
    print(f"边数: {result['cime_analysis']['edges']}")
else:
    print(f"分析失败: {result['error']}")
```

### 使用配置文件

```python
from topo_analyzer import Settings, TopologyAnalyzer

# 从YAML配置文件加载
settings = Settings.from_yaml("config.yaml")
analyzer = TopologyAnalyzer(settings)

# 运行分析
result = analyzer.run_complete_analysis()
```

## 配置

### 配置文件示例 (config.yaml)

```yaml
# 环境设置
environment: "development"
debug: true

# 文件路径配置
file_paths:
  data_dir: "./data"
  output_dir: "./out"
  cime_file: "./data/sample.cime"
  qs_file: "./data/sample.qs"

# 处理配置
processing:
  match_threshold: 80
  cime_encoding: "gbk"
  qs_encoding: "gbk"
  max_workers: 4
  consider_status: false

# 日志配置
logging:
  level: "INFO"
  file_path: "./logs/topo_analyzer.log"
```

### 环境变量

```bash
# 设置文件路径
export CIME_FILE="./data/sample.cime"
export QS_FILE="./data/sample.qs"
export OUTPUT_DIR="./output"

# 设置处理参数
export MATCH_THRESHOLD=85
export MAX_WORKERS=8

# 设置日志
export LOG_LEVEL="DEBUG"
export LOG_FILE_PATH="./logs/debug.log"
```

## 项目结构

```
topo-analyzer/
├── src/topo_analyzer/          # 源代码
│   ├── cime/                   # CIME解析模块
│   ├── qs/                     # QS解析和处理模块
│   ├── transfer/               # 拓扑转换和分析模块
│   ├── utils/                  # 工具模块
│   ├── config/                 # 配置管理
│   ├── cli.py                  # 命令行接口
│   └── main.py                 # 主程序
├── tests/                      # 测试代码
│   ├── unit/                   # 单元测试
│   └── integration/            # 集成测试
├── docs/                       # 文档
├── data/                       # 示例数据
├── out/                        # 输出目录
├── config.yaml                 # 配置文件
├── pyproject.toml             # 项目配置
└── README.md                  # 项目说明
```

## 开发

### 设置开发环境

```bash
# 克隆仓库
git clone https://github.com/your-org/topo-analyzer.git
cd topo-analyzer

# 安装开发依赖
pip install -e ".[dev]"

# 安装pre-commit钩子
pre-commit install
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 生成覆盖率报告
pytest --cov=src/topo_analyzer --cov-report=html
```

### 代码质量检查

```bash
# 代码格式化
black src/ tests/

# 导入排序
isort src/ tests/

# 静态类型检查
mypy src/

# 代码风格检查
flake8 src/ tests/
```

## API文档

详细的API文档请参考 [docs/api.md](docs/api.md)

## 示例

更多使用示例请参考 [docs/examples.md](docs/examples.md)

## 贡献

欢迎贡献代码！请参考 [CONTRIBUTING.md](CONTRIBUTING.md) 了解贡献指南。

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 更新日志

详见 [CHANGELOG.md](CHANGELOG.md)

## 支持

如果您遇到问题或有建议，请：

1. 查看 [FAQ](docs/faq.md)
2. 搜索 [Issues](https://github.com/your-org/topo-analyzer/issues)
3. 创建新的 Issue

## 致谢

感谢所有贡献者和支持者！
