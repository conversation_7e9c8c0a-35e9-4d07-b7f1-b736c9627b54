# logger_config.py
import logging
import sys


def get_logger(name: str = __name__, level=logging.INFO) -> logging.Logger:
    """
    获取统一配置的 logger
    """
    logger = logging.getLogger(name)
    if not logger.handlers:  # 避免重复添加 handler
        logger.setLevel(level)
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.propagate = False
    return logger
