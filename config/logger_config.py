# logger_config.py
import logging
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Optional

from .settings import settings


def get_logger(
    name: str = __name__,
    level: Optional[str] = None,
    file_path: Optional[str] = None
) -> logging.Logger:
    """
    获取统一配置的 logger

    Args:
        name: logger名称
        level: 日志级别，如果为None则使用配置文件中的级别
        file_path: 日志文件路径，如果为None则使用配置文件中的路径

    Returns:
        配置好的logger实例
    """
    logger = logging.getLogger(name)

    # 避免重复添加 handler
    if logger.handlers:
        return logger

    # 设置日志级别
    log_level = level or settings.logging.level
    logger.setLevel(getattr(logging, log_level.upper()))

    # 创建格式化器
    formatter = logging.Formatter(
        settings.logging.format,
        datefmt=settings.logging.date_format
    )

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器（如果配置了文件路径）
    log_file = file_path or settings.logging.file_path
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = RotatingFileHandler(
            log_path,
            maxBytes=settings.logging.max_file_size,
            backupCount=settings.logging.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    logger.propagate = False
    return logger
