# 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# 环境设置
ENVIRONMENT=development
DEBUG=true

# 文件路径
DATA_DIR=./data
OUTPUT_DIR=./out
CIME_FILE=./data/万州_20250829_1614.CIME
QS_FILE=./data/重庆_20250721_0000.QS

# 处理参数
MATCH_THRESHOLD=80
CIME_ENCODING=gbk
QS_ENCODING=gbk
MAX_WORKERS=4
CONSIDER_STATUS=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs/topo_analyzer.log
LOG_MAX_FILE_SIZE=10485760
LOG_BACKUP_COUNT=5

# 数据库配置（可选）
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=
DB_DATABASE=topo_analyzer
