import json
from typing import List, Dict, Any

class TopologyCleaner:
    """
    拓扑清洗器：剔除状态未知的节点和边
    """

    def __init__(self, topo_path: str):
        self.topo_path = topo_path
        self.nodes: List[Dict[str, Any]] = []
        self.edges: List[Dict[str, Any]] = []

        self.clean_nodes: List[Dict[str, Any]] = []
        self.clean_edges: List[Dict[str, Any]] = []

    def load_topology(self):
        """加载拓扑 JSON 文件"""
        with open(self.topo_path, "r", encoding="utf-8") as f:
            topo = json.load(f)
        self.nodes = topo.get("nodes", [])
        self.edges = topo.get("edges", [])

    def clean_nodes_edges(self):
        """剔除状态为 UNKNOWN 的节点和设备状态未知的边"""
        # 节点
        self.clean_nodes = [n for n in self.nodes]
        # clean_node_ids = {n["id"] for n in self.clean_nodes}

        # 边
        clean_edges = []
        for e in self.edges:
            from_raw = e.get("from", "")
            to_raw = e.get("to", "")

            from_id = from_raw.split(" ")[0] if from_raw else ""
            to_id = to_raw.split(" ")[0] if to_raw else ""

            # if from_id not in clean_node_ids or to_id not in clean_node_ids:
            #     continue

            devices_status = e.get("devices_status", [])
            if any(d.get("status") == "UNKNOWN" for d in devices_status):
                continue

            clean_edges.append(e)

        self.clean_edges = clean_edges

    def save_clean_topology(self, out_path: str):
        """保存干净拓扑 JSON"""
        clean_topo = {"nodes": self.clean_nodes, "edges": self.clean_edges}
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(clean_topo, f, ensure_ascii=False, indent=2)
        print(f"状态干净的拓扑已生成: {out_path}")
        print(f"节点数: {len(self.clean_nodes)}, 边数: {len(self.clean_edges)}")

    def run(self, out_path: str):
        """执行完整清洗流程"""
        self.load_topology()
        self.clean_nodes_edges()
        self.save_clean_topology(out_path)