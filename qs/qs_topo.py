import re
from collections import defaultdict, deque
import json
import time

CIME_FILE = "../data/重庆_20250721_0000.QS"

start_time = time.time()

# -----------------------------
# 读取文件
# -----------------------------
with open(CIME_FILE, "r", encoding="gbk") as f:
    lines = [line.rstrip("\n") for line in f if line.strip()]

# -----------------------------
# 解析 Tag 块
# -----------------------------
blocks = {}
current_tag = None
header = []

for line in lines:
    m_tag_start = re.match(r"<(\w+)(::.*)?>", line)
    m_tag_end = re.match(r"</(\w+)(::.*)?>", line)
    if m_tag_start:
        current_tag = m_tag_start.group(1)
        blocks[current_tag] = {"header": [], "data": []}
    elif m_tag_end:
        current_tag = None
    elif current_tag:
        if line.startswith("@"):
            header = re.split(r"\s+", line[1:].strip())
            blocks[current_tag]["header"] = header
        elif line.startswith("//"):
            continue
        else:
            values = re.split(r"\s+", line.lstrip("#").strip())
            if len(values) < len(header):
                values += [""] * (len(header) - len(values))
            blocks[current_tag]["data"].append(dict(zip(header, values)))

# -----------------------------
# 构建节点（只保留 name 包含“万州”的节点）
# -----------------------------
nds = {}
nd_type_map = {}

for bus in blocks.get("Bus", {}).get("data", []):
    bus_name = bus.get("name", "")
    if "万州" not in bus_name:
        continue
    nd_id = bus.get("nd")
    if nd_id:
        nds[nd_id] = bus_name
        nd_type_map[nd_id] = "Busbar"

for tr in blocks.get("Transformer", {}).get("data", []):
    transformer_name = tr.get("name", tr.get("id"))
    if "万州" not in transformer_name:
        continue
    for nd_key, suffix in [("I_nd", "_H"), ("K_nd", "_M"), ("J_nd", "_L")]:
        nd = tr.get(nd_key)
        if nd:
            nds[nd] = f"{transformer_name}{suffix}"
            nd_type_map[nd] = "Winding"

for load in blocks.get("Load", {}).get("data", []):
    load_name = load.get("name", "")
    if "万州" not in load_name:
        continue
    nd_id = load.get("nd")
    if nd_id:
        nds[nd_id] = load_name
        nd_type_map[nd_id] = "Load"

# -----------------------------
# 构建开关/刀闸映射
# -----------------------------
dual_end_tags = ["Breaker", "Disconnector"]
i_j_map = defaultdict(list)

for tag in dual_end_tags:
    for dev in blocks.get(tag, {}).get("data", []):
        i_nd = dev.get("I_nd")
        j_nd = dev.get("J_nd")
        name = dev.get("name", tag)
        if i_nd and j_nd:
            i_j_map[i_nd].append((name, j_nd))
            i_j_map[j_nd].append((name, i_nd))


# -----------------------------
# BFS 查找最近母线/绕组/负荷
# -----------------------------
def find_nearest_bus_winding_load(start_nd):
    visited = set([start_nd])
    queue = deque([start_nd])
    while queue:
        current = queue.popleft()
        if nd_type_map.get(current) in ["Busbar", "Winding", "Load"]:
            return current
        for _, neighbor in i_j_map.get(current, []):
            if neighbor not in visited:
                visited.add(neighbor)
                queue.append(neighbor)
    return None


# -----------------------------
# BFS 构建边
# -----------------------------
edges = []
edge_set = set()

for start_nd in nds.keys():
    queue = deque([(start_nd, [])])
    visited = set([start_nd])
    while queue:
        current, path = queue.popleft()
        current_type = nd_type_map.get(current)
        for dev_name, neighbor in i_j_map.get(current, []):
            neighbor_type = nd_type_map.get(neighbor)
            if neighbor in visited:
                continue
            visited.add(neighbor)
            new_path = path + [dev_name]
            if neighbor_type in ["Busbar", "Winding", "Load"] and new_path:
                key = frozenset([start_nd, neighbor])
                if key not in edge_set:
                    edge_set.add(key)
                    n1, n2 = sorted([start_nd, neighbor])
                    edges.append({
                        "from": f"{n1} [{nds[n1]}]",
                        "to": f"{n2} [{nds[n2]}]",
                        "devices": new_path
                    })
            queue.append((neighbor, new_path))

# -----------------------------
# 解析 ACline，两端节点通过 BFS 查找最近母线/绕组/负荷
# -----------------------------
line_edges = []

for line in blocks.get("ACline", {}).get("data", []):
    i_nd = line.get("I_nd")
    j_nd = line.get("J_nd")
    if not i_nd or not j_nd:
        continue

    i_target = find_nearest_bus_winding_load(i_nd)
    j_target = find_nearest_bus_winding_load(j_nd)
    if not i_target or not j_target:
        continue

    line_name = line.get("name", line.get("id", "Line"))
    n1 = f"{i_target} [{nds.get(i_target, i_target)}]"
    n2 = f"{j_target} [{nds.get(j_target, j_target)}]"

    key = frozenset([i_target, j_target])
    if key not in edge_set:
        edge_set.add(key)
        edges.append({
            "from": n1,
            "to": n2,
            "devices": [line_name]
        })
    line_edges.append({"from": n1, "to": n2, "line": line_name})

# -----------------------------
# 保存 JSON
# -----------------------------
all_edges = []

# 添加 bus_winding_edges
for e in edges:
    all_edges.append({
        "from": e["from"],
        "to": e["to"],
        "devices": e.get("devices", [])
    })

# 添加 line_edges
for e in line_edges:
    all_edges.append({
        "from": e["from"],
        "to": e["to"],
        "devices": [e.get("line", "Line")]
    })

topo_json = {
    "nodes": [{"id": nid, "name": nname, "type": nd_type_map.get(nid)} for nid, nname in nds.items()],
    "edges": all_edges
}

with open("../out/qs_topology.json", "w", encoding="utf-8") as f:
    json.dump(topo_json, f, ensure_ascii=False, indent=2)