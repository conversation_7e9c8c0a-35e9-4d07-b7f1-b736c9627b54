import json
from typing import List, Dict, Any

from utils.name_matcher import NameMatcher


class TopologyMatcher:
    """
    拓扑匹配器：将 QS 状态匹配到 CIME 拓扑中
    """

    def __init__(self, topo_path: str, qs_status_path: str, threshold: int = 80):
        self.topo_path = topo_path
        self.qs_status_path = qs_status_path
        self.threshold = threshold

        self.nodes: List[Dict[str, Any]] = []
        self.edges: List[Dict[str, Any]] = []
        self.node_status: Dict[str, str] = {}
        self.device_status: Dict[str, str] = {}

        self.unmatched_nodes: List[str] = []
        self.unmatched_devices: List[str] = []

    def load_files(self):
        """加载 QS 状态和 CIME 拓扑"""
        with open(self.qs_status_path, "r", encoding="utf-8") as f:
            qs_status = json.load(f)
        self.node_status = qs_status.get("nodes", {})
        self.device_status = qs_status.get("devices", {})

        with open(self.topo_path, "r", encoding="utf-8") as f:
            topo = json.load(f)
        self.nodes = topo.get("nodes", [])
        self.edges = topo.get("edges", [])

    def match_nodes(self):
        """节点匹配"""
        node_matcher = NameMatcher(list(self.node_status.keys()), threshold=self.threshold)
        for n in self.nodes:
            original_name = n.get("name")
            matched_name = node_matcher.match(original_name)
            if matched_name:
                n["status"] = self.node_status.get(matched_name, "UNKNOWN")
            else:
                n["status"] = "UNKNOWN"
                self.unmatched_nodes.append(original_name)
            n["matched_name"] = matched_name

    def match_devices(self):
        """设备匹配"""
        device_matcher = NameMatcher(list(self.device_status.keys()), threshold=self.threshold)
        for e in self.edges:
            devices_in_path = e.get("devices", [])
            device_states = []
            for dev_name in devices_in_path:
                matched_dev = device_matcher.match(dev_name)
                if matched_dev:
                    state = self.device_status.get(matched_dev, "UNKNOWN")
                else:
                    state = "UNKNOWN"
                    self.unmatched_devices.append(dev_name)
                device_states.append({
                    "name": dev_name,
                    "matched_name": matched_dev,
                    "status": state
                })
            e["devices_status"] = device_states

    def save_matched_topology(self, out_path: str):
        """保存带匹配信息的拓扑"""
        combined_topo = {"nodes": self.nodes, "edges": self.edges}
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(combined_topo, f, ensure_ascii=False, indent=2)

    def save_unmatched_elements(self, out_path: str):
        """保存未匹配元素"""
        unmatched_json = {
            "unmatched_nodes": list(set(self.unmatched_nodes)),
            "unmatched_devices": list(set(self.unmatched_devices))
        }
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(unmatched_json, f, ensure_ascii=False, indent=2)

    def set_transformer_edges_closed(self):
        """将所有 edge 中的 Transformer 设备状态设为 CLOSED"""
        for edge in self.edges:
            if "devices_status" not in edge:
                continue
            for dev_status in edge["devices_status"]:
                if dev_status["name"].startswith("Transformer-"):
                    dev_status["status"] = "CLOSED"

    def run(self, matched_topo_path: str, unmatched_path: str):
        """执行完整匹配流程"""
        self.load_files()
        self.match_nodes()
        self.match_devices()
        self.set_transformer_edges_closed()
        self.save_matched_topology(matched_topo_path)
        self.save_unmatched_elements(unmatched_path)
