import json
import re
from typing import Dict


class QSStatusParser:
    def __init__(self, qs_file: str, encoding="gbk"):
        self.qs_file = qs_file
        self.encoding = encoding
        self.blocks = {}
        self.node_status: Dict[str, str] = {}
        self.device_status: Dict[str, str] = {}

    def parse_file(self):
        """读取 QS 文件并按行去除空行"""
        with open(self.qs_file, "r", encoding=self.encoding) as f:
            self.lines = [line.rstrip() for line in f if line.strip()]

    def parse_blocks(self):
        """解析标签块，生成 {tag: {header, data}}"""
        current_tag = None
        header = []

        for line in self.lines:
            m_tag_start = re.match(r"<([^>]+)>", line)
            if m_tag_start:
                tag_full = m_tag_start.group(1)
                tag = tag_full.split("::")[0].strip()
                current_tag = tag
                self.blocks[current_tag] = {"header": [], "data": []}
                continue

            m_tag_end = re.match(r"</([^>]+)>", line)
            if m_tag_end:
                tag_full = m_tag_end.group(1)
                tag = tag_full.split("::")[0].strip()
                if current_tag == tag:
                    current_tag = None
                continue

            if current_tag:
                if line.startswith("@"):
                    header = re.split(r"\s+", line[1:].strip())
                    self.blocks[current_tag]["header"] = header
                elif line.startswith("#"):
                    values = re.split(r"\s+", line[1:].strip())
                    if len(values) < len(header):
                        values += [""] * (len(header) - len(values))
                    row = dict(zip(header, values))
                    if "id" in row:
                        row["id"] = row["id"].lstrip("#")
                    self.blocks[current_tag]["data"].append(row)

    def extract_node_status(self):
        """提取节点状态，CLOSED/OPEN"""
        # Bus
        for bus in self.blocks.get("Bus", {}).get("data", []):
            name = bus.get("name")
            if not name:
                continue
            status = bus.get("off", "0")
            self.node_status[name] = "CLOSED" if status in ["0"] else "OPEN"

        # Load
        for load in self.blocks.get("Load", {}).get("data", []):
            name = load.get("name")
            if not name:
                continue
            status = load.get("off", "1")
            self.node_status[name] = "CLOSED" if status in ["0"] else "OPEN"

        # Transformer 每个绕组端
        for tf in self.blocks.get("Transformer", {}).get("data", []):
            for side, side_name in zip(["I", "K", "J"], ["高", "中", "低"]):
                name = tf.get("name") + f"-{side_name}"
                if not name:
                    continue
                off = tf.get(f"{side}_off", "1")
                self.node_status[name] = "CLOSED" if off in ["0"] else "OPEN"

    def extract_device_status(self):
        """提取设备状态"""
        for tag in ["Breaker", "Disconnector"]:
            for dev in self.blocks.get(tag, {}).get("data", []):
                name = dev.get("name")
                if not name:
                    continue
                state = dev.get("point", "0")
                self.device_status[name] = "CLOSED" if state in ["1"] else "OPEN"

        # ACLine
        for line in self.blocks.get("ACline", {}).get("data", []):
            name = line.get("name")
            if not name:
                continue
            i_off = line.get("I_off", "0")
            j_off = line.get("J_off", "0")
            self.device_status[name] = "CLOSED" if i_off in ["0"] and j_off in ["0"] else "OPEN"

    def to_dict(self):
        return {
            "nodes": self.node_status,
            "devices": self.device_status
        }

    def save_json(self, path):
        with open(path, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)

    def run(self, output_path: str = None):
        """
        统一执行解析流程并返回状态字典
        如果 output_path 提供，则保存为 JSON 文件
        """
        self.parse_file()
        self.parse_blocks()
        self.extract_node_status()
        self.extract_device_status()
        if output_path:
            self.save_json(output_path)
