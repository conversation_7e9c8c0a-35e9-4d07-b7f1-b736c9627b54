"""
测试配置文件

提供测试用的fixtures和配置
"""

import json
import tempfile
from pathlib import Path
from typing import Dict, Any

import pytest

from src.topo_analyzer.config.settings import Settings


@pytest.fixture
def temp_dir():
    """临时目录fixture"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def test_settings(temp_dir):
    """测试用配置fixture"""
    settings = Settings()
    settings.file_paths.data_dir = temp_dir / "data"
    settings.file_paths.output_dir = temp_dir / "out"
    settings.ensure_directories()
    return settings


@pytest.fixture
def sample_cime_data():
    """示例CIME数据"""
    return """<BaseVoltage>
@mRID	name
v_1	10kV
v_2	35kV
</BaseVoltage>

<Substation>
@mRID	name	type	pathName
ss_1	万州变电站	变电站	万州变电站
</Substation>

<BusbarSection>
@mRID	name	I_node	BaseVoltage	Substation	pathName
bus_1	10kV母线1	n_1	v_1	ss_1	万州变电站/10kV母线1
</BusbarSection>

<Breaker>
@mRID	name	I_node	J_node	BaseVoltage	pathName
br_1	断路器1	n_1	n_2	v_1	万州变电站/断路器1
</Breaker>"""


@pytest.fixture
def sample_qs_data():
    """示例QS数据"""
    return """<Bus>
@name	off
万州变电站/10kV母线1	0
</Bus>

<Breaker>
@name	point
万州变电站/断路器1	1
</Breaker>"""


@pytest.fixture
def sample_cime_file(temp_dir, sample_cime_data):
    """示例CIME文件"""
    cime_file = temp_dir / "test.cime"
    with open(cime_file, "w", encoding="gbk") as f:
        f.write(sample_cime_data)
    return cime_file


@pytest.fixture
def sample_qs_file(temp_dir, sample_qs_data):
    """示例QS文件"""
    qs_file = temp_dir / "test.qs"
    with open(qs_file, "w", encoding="gbk") as f:
        f.write(sample_qs_data)
    return qs_file


@pytest.fixture
def sample_topology_data():
    """示例拓扑数据"""
    return {
        "nodes": [
            {
                "id": "n_1",
                "name": "万州变电站/10kV母线1",
                "type": "Busbar",
                "substation": "万州变电站",
                "voltage": "10kV"
            },
            {
                "id": "n_2", 
                "name": "万州变电站/10kV母线2",
                "type": "Busbar",
                "substation": "万州变电站",
                "voltage": "10kV"
            }
        ],
        "edges": [
            {
                "from": "n_1 [万州变电站/10kV母线1]",
                "to": "n_2 [万州变电站/10kV母线2]",
                "devices": ["万州变电站/断路器1"],
                "from_voltage": "10kV",
                "to_voltage": "10kV",
                "min_voltage": 10.0
            }
        ]
    }


@pytest.fixture
def sample_status_data():
    """示例状态数据"""
    return {
        "nodes": {
            "万州变电站/10kV母线1": "CLOSED",
            "万州变电站/10kV母线2": "CLOSED"
        },
        "devices": {
            "万州变电站/断路器1": "CLOSED"
        }
    }


@pytest.fixture
def sample_topology_file(temp_dir, sample_topology_data):
    """示例拓扑文件"""
    topo_file = temp_dir / "topology.json"
    with open(topo_file, "w", encoding="utf-8") as f:
        json.dump(sample_topology_data, f, ensure_ascii=False, indent=2)
    return topo_file


@pytest.fixture
def sample_status_file(temp_dir, sample_status_data):
    """示例状态文件"""
    status_file = temp_dir / "status.json"
    with open(status_file, "w", encoding="utf-8") as f:
        json.dump(sample_status_data, f, ensure_ascii=False, indent=2)
    return status_file


@pytest.fixture
def sample_matched_topology_data():
    """示例匹配后的拓扑数据"""
    return {
        "nodes": [
            {
                "id": "n_1",
                "name": "万州变电站/10kV母线1",
                "type": "Busbar",
                "substation": "万州变电站",
                "voltage": "10kV",
                "status": "CLOSED",
                "matched_name": "万州变电站/10kV母线1"
            },
            {
                "id": "n_2",
                "name": "万州变电站/10kV母线2", 
                "type": "Busbar",
                "substation": "万州变电站",
                "voltage": "10kV",
                "status": "CLOSED",
                "matched_name": "万州变电站/10kV母线2"
            }
        ],
        "edges": [
            {
                "from": "n_1 [万州变电站/10kV母线1]",
                "to": "n_2 [万州变电站/10kV母线2]",
                "devices": ["万州变电站/断路器1"],
                "from_voltage": "10kV",
                "to_voltage": "10kV",
                "min_voltage": 10.0,
                "devices_status": [
                    {
                        "name": "万州变电站/断路器1",
                        "status": "CLOSED",
                        "matched_name": "万州变电站/断路器1"
                    }
                ]
            }
        ]
    }


@pytest.fixture
def sample_matched_topology_file(temp_dir, sample_matched_topology_data):
    """示例匹配后的拓扑文件"""
    matched_file = temp_dir / "matched_topology.json"
    with open(matched_file, "w", encoding="utf-8") as f:
        json.dump(sample_matched_topology_data, f, ensure_ascii=False, indent=2)
    return matched_file
