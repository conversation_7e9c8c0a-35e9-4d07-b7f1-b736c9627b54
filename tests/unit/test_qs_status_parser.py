"""
QS状态解析器测试
"""

import json
import pytest

from src.topo_analyzer.qs.status_parser import <PERSON><PERSON>tatusPars<PERSON>, QSParseError


class TestQSStatusParser:
    """测试QS状态解析器"""
    
    def test_init_file_not_exists(self):
        """测试文件不存在的情况"""
        with pytest.raises(FileNotFoundError):
            QSStatusParser("nonexistent_file.qs")
    
    def test_init_success(self, sample_qs_file):
        """测试成功初始化"""
        parser = QSStatusParser(str(sample_qs_file))
        assert parser.qs_file == sample_qs_file
        assert parser.encoding == "gbk"  # 默认编码
        assert isinstance(parser.blocks, dict)
        assert isinstance(parser.node_status, dict)
        assert isinstance(parser.device_status, dict)
    
    def test_parse_file(self, sample_qs_file):
        """测试文件解析"""
        parser = QSStatusParser(str(sample_qs_file))
        parser.parse_file()
        
        assert len(parser.lines) > 0
        # 检查是否去除了空行
        assert all(line.strip() for line in parser.lines)
    
    def test_parse_blocks(self, sample_qs_file):
        """测试标签块解析"""
        parser = QSStatusParser(str(sample_qs_file))
        parser.parse_file()
        parser.parse_blocks()
        
        # 检查是否解析出了预期的块
        assert "Bus" in parser.blocks
        assert "Breaker" in parser.blocks
        
        # 检查Bus块的内容
        bus_block = parser.blocks["Bus"]
        assert "header" in bus_block
        assert "data" in bus_block
        assert len(bus_block["data"]) == 1  # 一个Bus记录
        
        # 检查数据内容
        bus_data = bus_block["data"][0]
        assert "name" in bus_data
        assert "off" in bus_data
        assert bus_data["name"] == "万州变电站/10kV母线1"
        assert bus_data["off"] == "0"
    
    def test_extract_node_status(self, sample_qs_file):
        """测试节点状态提取"""
        parser = QSStatusParser(str(sample_qs_file))
        parser.parse_file()
        parser.parse_blocks()
        parser.extract_node_status()
        
        # 检查Bus节点状态
        assert "万州变电站/10kV母线1" in parser.node_status
        assert parser.node_status["万州变电站/10kV母线1"] == "CLOSED"  # off=0表示闭合
    
    def test_extract_device_status(self, sample_qs_file):
        """测试设备状态提取"""
        parser = QSStatusParser(str(sample_qs_file))
        parser.parse_file()
        parser.parse_blocks()
        parser.extract_device_status()
        
        # 检查Breaker设备状态
        assert "万州变电站/断路器1" in parser.device_status
        assert parser.device_status["万州变电站/断路器1"] == "CLOSED"  # point=1表示闭合
    
    def test_to_dict(self, sample_qs_file):
        """测试转换为字典"""
        parser = QSStatusParser(str(sample_qs_file))
        parser.parse_file()
        parser.parse_blocks()
        parser.extract_node_status()
        parser.extract_device_status()
        
        result = parser.to_dict()
        
        assert "nodes" in result
        assert "devices" in result
        assert isinstance(result["nodes"], dict)
        assert isinstance(result["devices"], dict)
        
        # 检查内容
        assert "万州变电站/10kV母线1" in result["nodes"]
        assert "万州变电站/断路器1" in result["devices"]
    
    def test_save_json(self, sample_qs_file, temp_dir):
        """测试保存JSON"""
        parser = QSStatusParser(str(sample_qs_file))
        parser.parse_file()
        parser.parse_blocks()
        parser.extract_node_status()
        parser.extract_device_status()
        
        output_file = temp_dir / "test_status.json"
        parser.save_json(str(output_file))
        
        assert output_file.exists()
        
        # 验证保存的内容
        with open(output_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        assert "nodes" in data
        assert "devices" in data
        assert "万州变电站/10kV母线1" in data["nodes"]
        assert "万州变电站/断路器1" in data["devices"]
    
    def test_run_complete_flow(self, sample_qs_file, temp_dir):
        """测试完整流程"""
        parser = QSStatusParser(str(sample_qs_file))
        
        output_file = temp_dir / "status_output.json"
        result = parser.run(str(output_file))
        
        # 检查返回结果
        assert "nodes" in result
        assert "devices" in result
        assert len(result["nodes"]) > 0
        assert len(result["devices"]) > 0
        
        # 检查文件是否生成
        assert output_file.exists()
        
        # 验证文件内容
        with open(output_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        assert data == result
    
    def test_run_without_output(self, sample_qs_file):
        """测试不保存文件的运行"""
        parser = QSStatusParser(str(sample_qs_file))
        result = parser.run()  # 不指定输出路径
        
        # 应该返回结果但不保存文件
        assert "nodes" in result
        assert "devices" in result
    
    def test_get_statistics(self, sample_qs_file):
        """测试获取统计信息"""
        parser = QSStatusParser(str(sample_qs_file))
        parser.run()
        
        stats = parser.get_statistics()
        
        assert "total_nodes" in stats
        assert "total_devices" in stats
        assert "node_status_distribution" in stats
        assert "device_status_distribution" in stats
        
        # 检查统计数据
        assert stats["total_nodes"] == len(parser.node_status)
        assert stats["total_devices"] == len(parser.device_status)
        
        # 检查状态分布
        node_dist = stats["node_status_distribution"]
        assert "CLOSED" in node_dist or "OPEN" in node_dist
        
        device_dist = stats["device_status_distribution"]
        assert "CLOSED" in device_dist or "OPEN" in device_dist
    
    def test_status_interpretation(self, temp_dir):
        """测试状态解释的正确性"""
        # 创建包含各种状态的QS文件
        qs_content = """<Bus>
@name	off
母线1	0
母线2	1
</Bus>

<Breaker>
@name	point
断路器1	1
断路器2	0
</Breaker>

<ACline>
@name	I_off	J_off
线路1	0	0
线路2	1	0
线路3	0	1
</ACline>"""
        
        qs_file = temp_dir / "status_test.qs"
        with open(qs_file, "w", encoding="gbk") as f:
            f.write(qs_content)
        
        parser = QSStatusParser(str(qs_file))
        result = parser.run()
        
        # 检查Bus状态解释
        assert result["nodes"]["母线1"] == "CLOSED"  # off=0
        assert result["nodes"]["母线2"] == "OPEN"    # off=1
        
        # 检查Breaker状态解释
        assert result["devices"]["断路器1"] == "CLOSED"  # point=1
        assert result["devices"]["断路器2"] == "OPEN"    # point=0
        
        # 检查ACline状态解释
        assert result["devices"]["线路1"] == "CLOSED"  # 两端都是0
        assert result["devices"]["线路2"] == "OPEN"    # 有一端是1
        assert result["devices"]["线路3"] == "OPEN"    # 有一端是1
    
    def test_error_handling_invalid_file(self, temp_dir):
        """测试错误处理 - 无效文件"""
        # 创建一个无效的QS文件
        invalid_file = temp_dir / "invalid.qs"
        with open(invalid_file, "w", encoding="utf-8") as f:
            f.write("这不是有效的QS格式")
        
        parser = QSStatusParser(str(invalid_file))
        
        # 应该能够处理无效格式而不崩溃
        try:
            result = parser.run()
            # 即使文件格式无效，也应该能够完成解析（可能结果为空）
            assert isinstance(result, dict)
            assert "nodes" in result
            assert "devices" in result
        except QSParseError:
            # 或者抛出明确的解析错误
            pass
    
    def test_encoding_parameter(self, sample_qs_file):
        """测试编码参数"""
        parser = QSStatusParser(str(sample_qs_file), encoding="utf-8")
        assert parser.encoding == "utf-8"
        
        # 使用默认编码
        parser_default = QSStatusParser(str(sample_qs_file))
        assert parser_default.encoding == "gbk"  # 配置中的默认值
