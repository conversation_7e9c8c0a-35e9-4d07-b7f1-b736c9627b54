"""
名称匹配器测试
"""

import pytest

from src.topo_analyzer.utils.name_matcher import NameMatcher, normalize_name


class TestNormalizeName:
    """测试名称标准化函数"""
    
    def test_normalize_empty_name(self):
        """测试空名称"""
        assert normalize_name("") == ""
        assert normalize_name(None) == ""
    
    def test_normalize_basic(self):
        """测试基本标准化"""
        assert normalize_name("test name") == "TESTNAME"
        assert normalize_name("  Test  Name  ") == "TESTNAME"
    
    def test_normalize_roman_numbers(self):
        """测试罗马数字转换"""
        assert normalize_name("线路Ⅰ") == "线路I"
        assert normalize_name("变压器Ⅱ") == "变压器II"
        assert normalize_name("母线Ⅲ") == "母线III"
    
    def test_normalize_synonyms(self):
        """测试同义词替换"""
        assert normalize_name("旁路开关") == "旁母开关"
        assert normalize_name("旁路") == "旁母"
    
    def test_normalize_combined(self):
        """测试组合标准化"""
        assert normalize_name("旁路 Ⅰ 开关") == "旁母I开关"


class TestNameMatcher:
    """测试名称匹配器"""
    
    def test_init_empty_references(self):
        """测试空参考列表初始化"""
        with pytest.raises(ValueError, match="参考名称列表不能为空"):
            NameMatcher([])
    
    def test_init_invalid_threshold(self):
        """测试无效阈值"""
        with pytest.raises(ValueError, match="阈值必须在0-100之间"):
            NameMatcher(["test"], threshold=-1)
        
        with pytest.raises(ValueError, match="阈值必须在0-100之间"):
            NameMatcher(["test"], threshold=101)
    
    def test_exact_match_original(self):
        """测试精确匹配原始名称"""
        matcher = NameMatcher(["万州变电站/10kV母线1", "万州变电站/断路器1"])
        
        assert matcher.match("万州变电站/10kV母线1") == "万州变电站/10kV母线1"
        assert matcher.match("万州变电站/断路器1") == "万州变电站/断路器1"
    
    def test_exact_match_normalized(self):
        """测试标准化后精确匹配"""
        matcher = NameMatcher(["万州变电站/10kV母线1"])
        
        assert matcher.match("万州变电站 / 10kV 母线1") == "万州变电站/10kV母线1"
        assert matcher.match("万州变电站/10KV母线1") == "万州变电站/10kV母线1"
    
    def test_fuzzy_match(self):
        """测试模糊匹配"""
        matcher = NameMatcher(["万州变电站/10kV母线1"], threshold=80)
        
        # 应该能匹配相似的名称
        assert matcher.match("万州变电站10kV母线1") == "万州变电站/10kV母线1"
        assert matcher.match("万州变电站/10kV母线一") is not None
    
    def test_no_match(self):
        """测试无匹配"""
        matcher = NameMatcher(["万州变电站/10kV母线1"], threshold=80)
        
        assert matcher.match("完全不同的名称") is None
        assert matcher.match("") is None
    
    def test_match_with_score(self):
        """测试带分数的匹配"""
        matcher = NameMatcher(["万州变电站/10kV母线1"])
        
        # 精确匹配
        result = matcher.match_with_score("万州变电站/10kV母线1")
        assert result is not None
        assert result["matched_name"] == "万州变电站/10kV母线1"
        assert result["score"] == 100
        assert result["match_type"] == "exact_original"
        
        # 标准化后精确匹配
        result = matcher.match_with_score("万州变电站 / 10kV 母线1")
        assert result is not None
        assert result["matched_name"] == "万州变电站/10kV母线1"
        assert result["score"] == 100
        assert result["match_type"] == "exact_normalized"
        
        # 无匹配
        result = matcher.match_with_score("完全不同的名称")
        assert result is None
    
    def test_batch_match(self):
        """测试批量匹配"""
        matcher = NameMatcher(["万州变电站/10kV母线1", "万州变电站/断路器1"])
        
        names = ["万州变电站/10kV母线1", "万州变电站/断路器1", "不存在的设备"]
        results = matcher.batch_match(names)
        
        assert len(results) == 3
        assert results["万州变电站/10kV母线1"] == "万州变电站/10kV母线1"
        assert results["万州变电站/断路器1"] == "万州变电站/断路器1"
        assert results["不存在的设备"] is None
    
    def test_threshold_effect(self):
        """测试阈值对匹配的影响"""
        reference_names = ["万州变电站/10kV母线1"]
        
        # 高阈值匹配器
        high_threshold_matcher = NameMatcher(reference_names, threshold=95)
        # 低阈值匹配器
        low_threshold_matcher = NameMatcher(reference_names, threshold=50)
        
        test_name = "万州变电站10kV母线"  # 相似但不完全相同
        
        # 高阈值可能不匹配
        high_result = high_threshold_matcher.match(test_name)
        # 低阈值应该能匹配
        low_result = low_threshold_matcher.match(test_name)
        
        # 低阈值匹配器更容易匹配
        assert low_result is not None
    
    def test_roman_number_matching(self):
        """测试罗马数字匹配"""
        matcher = NameMatcher(["线路I", "变压器II"])
        
        assert matcher.match("线路Ⅰ") == "线路I"
        assert matcher.match("变压器Ⅱ") == "变压器II"
    
    def test_synonym_matching(self):
        """测试同义词匹配"""
        matcher = NameMatcher(["旁母开关"])
        
        assert matcher.match("旁路开关") == "旁母开关"
