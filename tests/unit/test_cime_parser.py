"""
CIME解析器测试
"""

import json
import pytest

from src.topo_analyzer.cime.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, CIMEParseError


class TestCIMEParser:
    """测试CIME解析器"""
    
    def test_init_file_not_exists(self):
        """测试文件不存在的情况"""
        with pytest.raises(FileNotFoundError):
            CIMEParser("nonexistent_file.cime")
    
    def test_init_success(self, sample_cime_file):
        """测试成功初始化"""
        parser = CIMEParser(str(sample_cime_file))
        assert parser.cime_file == sample_cime_file
        assert parser.encoding == "gbk"  # 默认编码
        assert isinstance(parser.blocks, dict)
        assert isinstance(parser.nodes, dict)
    
    def test_parse_file(self, sample_cime_file):
        """测试文件解析"""
        parser = CIMEParser(str(sample_cime_file))
        parser.parse_file()
        
        # 检查是否解析出了预期的块
        assert "BaseVoltage" in parser.blocks
        assert "Substation" in parser.blocks
        assert "BusbarSection" in parser.blocks
        assert "Breaker" in parser.blocks
        
        # 检查BaseVoltage块的内容
        base_voltage_block = parser.blocks["BaseVoltage"]
        assert "header" in base_voltage_block
        assert "data" in base_voltage_block
        assert len(base_voltage_block["data"]) == 2  # 两个电压等级
    
    def test_parse_base_voltages(self, sample_cime_file):
        """测试电压等级解析"""
        parser = CIMEParser(str(sample_cime_file))
        parser.parse_file()
        parser.parse_base_voltages()
        
        assert "v_1" in parser.base_voltage_map
        assert "v_2" in parser.base_voltage_map
        assert parser.base_voltage_map["v_1"] == "10kV"
        assert parser.base_voltage_map["v_2"] == "35kV"
    
    def test_parse_substations(self, sample_cime_file):
        """测试变电站解析"""
        parser = CIMEParser(str(sample_cime_file))
        parser.parse_file()
        parser.parse_substations()
        
        assert "ss_1" in parser.substations
        assert parser.substations["ss_1"] == "万州变电站"
    
    def test_parse_nodes(self, sample_cime_file):
        """测试节点解析"""
        parser = CIMEParser(str(sample_cime_file))
        parser.parse_file()
        parser.parse_base_voltages()
        parser.parse_substations()
        parser.parse_nodes()
        
        assert "n_1" in parser.nodes
        assert parser.nodes["n_1"] == "万州变电站/10kV母线1"
        assert parser.node_type_map["n_1"] == "Busbar"
        assert parser.node_substation_map["n_1"] == "万州变电站"
        assert parser.node_voltage_map["n_1"] == "10kV"
    
    def test_parse_devices(self, sample_cime_file):
        """测试设备解析"""
        parser = CIMEParser(str(sample_cime_file))
        parser.parse_file()
        parser.parse_devices()
        
        # 检查i_j_map是否正确建立
        assert "n_1" in parser.i_j_map
        assert len(parser.i_j_map["n_1"]) > 0
        
        # 检查设备连接
        device_name, connected_node = parser.i_j_map["n_1"][0]
        assert device_name == "万州变电站/断路器1"
        assert connected_node == "n_2"
    
    def test_to_dict(self, sample_cime_file):
        """测试转换为字典"""
        parser = CIMEParser(str(sample_cime_file))
        parser.parse_file()
        parser.parse_base_voltages()
        parser.parse_substations()
        parser.parse_nodes()
        
        result = parser.to_dict()
        
        assert "nodes" in result
        assert "edges" in result
        assert isinstance(result["nodes"], list)
        assert isinstance(result["edges"], list)
        
        # 检查节点数据结构
        if result["nodes"]:
            node = result["nodes"][0]
            assert "id" in node
            assert "name" in node
            assert "type" in node
            assert "substation" in node
            assert "voltage" in node
    
    def test_save_json(self, sample_cime_file, temp_dir):
        """测试保存JSON"""
        parser = CIMEParser(str(sample_cime_file))
        parser.parse_file()
        parser.parse_base_voltages()
        parser.parse_substations()
        parser.parse_nodes()
        
        output_file = temp_dir / "test_output.json"
        parser.save_json(str(output_file))
        
        assert output_file.exists()
        
        # 验证保存的内容
        with open(output_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        assert "nodes" in data
        assert "edges" in data
    
    def test_run_complete_flow(self, sample_cime_file, temp_dir):
        """测试完整流程"""
        parser = CIMEParser(str(sample_cime_file))
        
        output_full = temp_dir / "full_topology.json"
        output_single = temp_dir / "single_nodes.json"
        
        result = parser.run(str(output_full), str(output_single))
        
        # 检查返回结果
        assert "nodes" in result
        assert "edges" in result
        
        # 检查文件是否生成
        assert output_full.exists()
        assert output_single.exists()
        
        # 验证文件内容
        with open(output_full, "r", encoding="utf-8") as f:
            full_data = json.load(f)
        assert "nodes" in full_data
        assert "edges" in full_data
        
        with open(output_single, "r", encoding="utf-8") as f:
            single_data = json.load(f)
        assert isinstance(single_data, list)
    
    def test_error_handling_invalid_file(self, temp_dir):
        """测试错误处理 - 无效文件"""
        # 创建一个无效的CIME文件
        invalid_file = temp_dir / "invalid.cime"
        with open(invalid_file, "w", encoding="utf-8") as f:
            f.write("这不是有效的CIME格式")
        
        parser = CIMEParser(str(invalid_file))
        
        # 应该能够处理无效格式而不崩溃
        try:
            parser.parse_file()
            # 即使文件格式无效，也应该能够完成解析（可能结果为空）
            assert isinstance(parser.blocks, dict)
        except CIMEParseError:
            # 或者抛出明确的解析错误
            pass
    
    def test_encoding_parameter(self, sample_cime_file):
        """测试编码参数"""
        parser = CIMEParser(str(sample_cime_file), encoding="utf-8")
        assert parser.encoding == "utf-8"
        
        # 使用默认编码
        parser_default = CIMEParser(str(sample_cime_file))
        assert parser_default.encoding == "gbk"  # 配置中的默认值
