from rapidfuzz import process, fuzz

ROMAN_MAP = {
    "Ⅰ": "I", "Ⅱ": "II", "Ⅲ": "III", "Ⅳ": "IV", "Ⅴ": "V", "Ⅵ": "VI"
}

SYNONYMS = {
    "旁路": "旁母",
    # 可以继续添加同义词
}

def normalize_name(name: str) -> str:
    """标准化名称：同义词替换、罗马数字转换、去空格、大写，不删除电压/母线"""
    if not name:
        return ""
    name = name.upper().replace(" ", "")
    for k, v in ROMAN_MAP.items():
        name = name.replace(k, v)
    for k, v in SYNONYMS.items():
        name = name.replace(k.upper(), v.upper())
    return name

class NameMatcher:
    def __init__(self, reference_names, threshold=80):
        """
        :param reference_names: 参考节点或设备列表
        :param threshold: rapidfuzz 模糊匹配阈值
        """
        self.threshold = threshold
        self.ref_names_raw = reference_names
        self.ref_names_set = set(reference_names)  # 快速精准匹配
        self.ref_names_norm = [normalize_name(n) for n in reference_names]

    def match(self, name: str):
        if not name:
            return None

        # 1. 精准匹配原始名称
        if name in self.ref_names_set:
            return name

        # 2. 标准化后精准匹配
        norm_name = normalize_name(name)
        for ref_norm, ref_raw in zip(self.ref_names_norm, self.ref_names_raw):
            if norm_name == ref_norm:
                return ref_raw

        # 3. 标准化后模糊匹配
        match = process.extractOne(norm_name, self.ref_names_norm, scorer=fuzz.ratio)
        if match:
            ref_match_norm, score, idx = match
            if score >= self.threshold:
                return self.ref_names_raw[idx]

        # 无匹配
        return None

